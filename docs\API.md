# App Builder API Documentation

This document provides comprehensive documentation for the App Builder API, including REST API endpoints, WebSocket endpoints, authentication, and real-time collaboration features.

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
   - [CSRF Token Authentication](#csrf-token-authentication)
   - [JWT Authentication](#jwt-authentication)
   - [Token Authentication](#token-authentication)
3. [HTTP REST API](#http-rest-api)
   - [Base URLs and Versioning](#base-urls-and-versioning)
   - [Request/Response Format](#requestresponse-format)
   - [Error Handling](#error-handling)
   - [Rate Limiting](#rate-limiting)
   - [App Management](#app-management)
   - [Template Management](#template-management)
   - [User Management](#user-management)
   - [AI Features](#ai-features)
   - [Health and Monitoring](#health-and-monitoring)
4. [WebSocket API](#websocket-api)
   - [Connection](#connection)
   - [Message Format](#message-format)
   - [App Builder WebSocket](#app-builder-websocket)
   - [Collaboration WebSocket](#collaboration-websocket)
   - [AI Suggestions WebSocket](#ai-suggestions-websocket)
   - [Notifications WebSocket](#notifications-websocket)
   - [Performance Monitoring WebSocket](#performance-monitoring-websocket)
5. [GraphQL API](#graphql-api)
6. [Security](#security)
7. [Development vs Production](#development-vs-production)
8. [Integration Examples](#integration-examples)

## Overview

The App Builder API provides a comprehensive set of endpoints for building, managing, and collaborating on web applications. The API supports both REST and real-time WebSocket communication, with features including:

- **App Management**: Create, read, update, and delete applications
- **Template System**: Reusable component, layout, and app templates
- **Real-time Collaboration**: Multi-user editing with live updates
- **AI-Powered Features**: Layout suggestions and component recommendations
- **Version Control**: App versioning and history management
- **Authentication**: Multiple authentication methods (CSRF, JWT, Token)
- **Performance Monitoring**: Real-time performance metrics

## Authentication

The App Builder API supports multiple authentication methods to accommodate different use cases and security requirements.

### CSRF Token Authentication

For web applications using session-based authentication, CSRF tokens are required for state-changing operations.

#### Get CSRF Token

```http
GET /api/csrf-token/
```

**Response:**
```json
{
  "csrfToken": "abc123def456..."
}
```

**Usage:**
Include the CSRF token in requests using one of these methods:
- Header: `X-CSRFToken: <token>`
- Form field: `csrfmiddlewaretoken=<token>`
- Cookie: `csrftoken=<token>` (automatically handled by browsers)

### JWT Authentication

JSON Web Tokens provide stateless authentication suitable for mobile apps and SPAs.

#### Login with JWT

```http
POST /api/auth/login/
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "username": "your_username",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe"
  },
  "expires_at": "2024-01-01T12:00:00Z"
}
```

#### Register User

```http
POST /api/auth/register/
Content-Type: application/json

{
  "username": "new_user",
  "email": "<EMAIL>",
  "password": "secure_password",
  "first_name": "John",
  "last_name": "Doe"
}
```

**Rate Limit:** 3 requests per hour per IP

#### Using JWT Tokens

Include the JWT token in the Authorization header:
```http
Authorization: Bearer <jwt_token>
```

### Token Authentication

Traditional DRF token authentication for API access.

#### Get Auth Token

```http
POST /api/token-auth/
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "token": "abc123def456...",
  "user_id": 1,
  "email": "<EMAIL>"
}
```

**Usage:**
```http
Authorization: Token <token>
```

## HTTP REST API

### Base URLs and Versioning

The API supports versioning through URL paths:

- **Base URL:** `/api/`
- **Version 1:** `/api/v1/` (current stable)
- **Version 2:** `/api/v2/` (future use)
- **Unversioned:** `/api/` (defaults to v1 for backward compatibility)

### Request/Response Format

#### Content Types
- **Request:** `application/json`
- **Response:** `application/json`

#### Standard Response Structure

**Success Response:**
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

**Error Response:**
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": { ... }
  },
  "status": 400
}
```

#### Pagination

List endpoints support pagination with the following parameters:
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 20, max: 100)

**Paginated Response:**
```json
{
  "count": 150,
  "next": "http://api.example.com/api/v1/apps/?page=3",
  "previous": "http://api.example.com/api/v1/apps/?page=1",
  "results": [ ... ]
}
```

### Error Handling

#### HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | OK - Request successful |
| 201 | Created - Resource created successfully |
| 400 | Bad Request - Invalid request data |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |

#### Error Response Examples

**Validation Error:**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "name": ["This field is required."],
      "email": ["Enter a valid email address."]
    }
  },
  "status": 400
}
```

**Authentication Error:**
```json
{
  "success": false,
  "error": {
    "code": "AUTHENTICATION_REQUIRED",
    "message": "Authentication credentials were not provided",
    "details": {}
  },
  "status": 401
}
```

### Rate Limiting

Rate limits are applied per IP address and authenticated user:

| Endpoint Category | Limit | Window |
|------------------|-------|---------|
| Authentication | 5 requests | 1 minute |
| User Registration | 3 requests | 1 hour |
| General API | 100 requests | 1 minute |
| AI Features | 10 requests | 1 minute |

**Rate Limit Headers:**
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

### App Management

#### List Apps

```http
GET /api/v1/apps/
```

**Query Parameters:**
- `search`: Search by name or description
- `is_public`: Filter by public/private apps
- `user`: Filter by user ID
- `ordering`: Sort by `name`, `created_at`, `-created_at`

**Response:**
```json
{
  "count": 25,
  "next": "http://api.example.com/api/v1/apps/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "My App",
      "description": "A sample application",
      "user": 1,
      "app_data": "{\"components\": [], \"layouts\": []}",
      "app_data_json": {
        "components": [],
        "layouts": []
      },
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:30:00Z",
      "is_public": false
    }
  ]
}
```

#### Create App

```http
POST /api/v1/apps/
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "New App",
  "description": "My new application",
  "app_data": {
    "components": [],
    "layouts": [],
    "styles": {},
    "data": {}
  },
  "is_public": false
}
```

#### Get App Details

```http
GET /api/v1/apps/{id}/
```

#### Update App

```http
PUT /api/v1/apps/{id}/
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Updated App Name",
  "description": "Updated description",
  "app_data": {
    "components": [...],
    "layouts": [...],
    "styles": {...},
    "data": {...}
  }
}
```

#### Delete App

```http
DELETE /api/v1/apps/{id}/
Authorization: Bearer <token>
```

#### App Versioning

**Create Version:**
```http
POST /api/v1/apps/{id}/create_version/
Content-Type: application/json

{
  "version_name": "v1.1.0",
  "description": "Added new features"
}
```

**List Versions:**
```http
GET /api/v1/apps/{id}/versions/
```

**Restore Version:**
```http
POST /api/v1/apps/{id}/restore_version/
Content-Type: application/json

{
  "version_id": 5
}
```

### Template Management

#### Component Templates

**List Component Templates:**
```http
GET /api/v1/component-templates/
```

**Query Parameters:**
- `search`: Search by name or description
- `component_type`: Filter by component type
- `is_public`: Filter public templates

**Create Component Template:**
```http
POST /api/v1/component-templates/
Content-Type: application/json

{
  "name": "Custom Button",
  "description": "A reusable button component",
  "component_type": "Button",
  "default_props": {
    "text": "Click Me",
    "variant": "primary",
    "size": "medium"
  },
  "is_public": false
}
```

#### Layout Templates

**List Layout Templates:**
```http
GET /api/v1/layout-templates/
```

**Create Layout Template:**
```http
POST /api/v1/layout-templates/
Content-Type: application/json

{
  "name": "Three Column Grid",
  "description": "A responsive three-column layout",
  "layout_type": "grid",
  "components": {
    "slots": ["header", "main", "sidebar"],
    "configuration": {
      "columns": 3,
      "gap": "1rem"
    }
  },
  "default_props": {
    "responsive": true,
    "minColumnWidth": "200px"
  }
}
```

#### App Templates

**List App Templates:**
```http
GET /api/v1/app-templates/
```

**Query Parameters:**
- `app_category`: Filter by category (business, portfolio, blog, etc.)
- `search`: Search by name or description

**Create App Template:**
```http
POST /api/v1/app-templates/
Content-Type: application/json

{
  "name": "Business Landing Page",
  "description": "Complete landing page template",
  "app_category": "business",
  "components": {
    "header": {...},
    "hero": {...},
    "features": {...},
    "footer": {...}
  },
  "required_components": ["Header", "Hero", "FeatureGrid", "Footer"],
  "preview_image": "https://example.com/preview.jpg"
}
```

#### Featured Templates

```http
GET /api/featured-templates/
```

**Response:**
```json
{
  "components": [...],
  "layouts": [...],
  "apps": [...]
}
```

### User Management

#### Get User Profile

```http
GET /api/auth/profile/
Authorization: Bearer <token>
```

**Response:**
```json
{
  "id": 1,
  "username": "john_doe",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "date_joined": "2024-01-01T12:00:00Z",
  "is_active": true
}
```

#### Update User Profile

```http
PUT /api/auth/profile/update/
Content-Type: application/json
Authorization: Bearer <token>

{
  "first_name": "John",
  "last_name": "Smith",
  "email": "<EMAIL>"
}
```

### AI Features

#### Generate AI Suggestions

```http
POST /api/generate_ai_suggestions/
Content-Type: application/json
Authorization: Bearer <token>

{
  "components": [...],
  "layouts": [...],
  "context": {
    "app_type": "business",
    "target_audience": "professionals"
  }
}
```

**Rate Limit:** 10 requests per minute

#### Generate Image

```http
POST /api/generate_image/
Content-Type: application/json
Authorization: Bearer <token>

{
  "prompt": "A modern business logo",
  "style": "professional",
  "size": "512x512"
}
```

### Health and Monitoring

#### Health Check

```http
GET /api/health/
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0",
  "database": "connected",
  "redis": "connected"
}
```

#### API Status

```http
GET /api/status/
```

#### Error Reporting

```http
POST /api/errors/
Content-Type: application/json

{
  "error_type": "javascript_error",
  "message": "TypeError: Cannot read property 'x' of undefined",
  "stack_trace": "...",
  "user_agent": "Mozilla/5.0...",
  "url": "/app-builder"
}
```

## WebSocket API

The WebSocket API provides real-time communication for collaborative editing, AI suggestions, notifications, and performance monitoring.

### Connection

WebSocket connections are established using the following endpoints:

#### App Builder WebSocket
```
ws://<host>/ws/app_builder/
wss://<host>/ws/app_builder/  (secure)
```

#### App-Specific WebSocket
```
ws://<host>/ws/app/{app_id}/
```

#### Collaboration WebSocket
```
ws://<host>/ws/collaboration/{session_id}/
```

#### AI Suggestions WebSocket
```
ws://<host>/ws/ai-suggestions/
```

#### Notifications WebSocket
```
ws://<host>/ws/notifications/
```

#### Performance Monitoring WebSocket
```
ws://<host>/ws/performance/
```

### Message Format

All WebSocket messages use JSON format:

```json
{
  "type": "message_type",
  "data": {
    // Message-specific data
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### App Builder WebSocket

#### Connection Authentication

Include authentication in the connection:
- **Query Parameter:** `?token=<jwt_token>`
- **Header:** `Authorization: Bearer <jwt_token>`

#### Client-to-Server Messages

**Ping:**
```json
{
  "type": "ping",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Request App Data:**
```json
{
  "type": "request_app_data",
  "app_id": 123
}
```

**Update App Data:**
```json
{
  "type": "update_app_data",
  "data": {
    "components": [...],
    "layouts": [...],
    "styles": {...}
  }
}
```

#### Server-to-Client Messages

**Pong:**
```json
{
  "type": "pong",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**App Data:**
```json
{
  "type": "app_data",
  "data": {
    "components": [...],
    "layouts": [...],
    "styles": {...},
    "data": {...}
  }
}
```

**Error:**
```json
{
  "type": "error",
  "message": "Authentication required",
  "code": "AUTH_REQUIRED"
}
```

### Collaboration WebSocket

Real-time multi-user collaboration features.

#### Client-to-Server Messages

**Join Session:**
```json
{
  "type": "join_session",
  "session_id": "session_123",
  "user_info": {
    "username": "john_doe",
    "avatar": "https://example.com/avatar.jpg"
  }
}
```

**Cursor Update:**
```json
{
  "type": "cursor_update",
  "position": {
    "x": 150,
    "y": 200
  },
  "component_id": "button_1"
}
```

**Edit Operation:**
```json
{
  "type": "edit_operation",
  "operation": {
    "type": "component_update",
    "component_id": "button_1",
    "property": "text",
    "value": "New Text",
    "old_value": "Old Text"
  }
}
```

**Chat Message:**
```json
{
  "type": "chat_message",
  "message": "Hello everyone!"
}
```

**Create Comment:**
```json
{
  "type": "create_comment",
  "content": "This needs to be bigger",
  "component_id": "button_1",
  "canvas_position": {
    "x": 100,
    "y": 150
  }
}
```

#### Server-to-Client Messages

**User Joined:**
```json
{
  "type": "user_joined",
  "user": {
    "id": 123,
    "username": "jane_doe",
    "avatar": "https://example.com/avatar2.jpg"
  },
  "session_participants": [...]
}
```

**Cursor Broadcast:**
```json
{
  "type": "cursor_broadcast",
  "user_id": 123,
  "username": "jane_doe",
  "position": {
    "x": 150,
    "y": 200
  },
  "component_id": "button_1"
}
```

**Edit Broadcast:**
```json
{
  "type": "edit_broadcast",
  "user_id": 123,
  "operation": {
    "type": "component_update",
    "component_id": "button_1",
    "property": "text",
    "value": "New Text"
  }
}
```

**Chat Broadcast:**
```json
{
  "type": "chat_message",
  "user_id": 123,
  "username": "jane_doe",
  "message": "Hello everyone!",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### AI Suggestions WebSocket

Real-time AI-powered layout and component suggestions.

#### Client-to-Server Messages

**Get Layout Suggestions:**
```json
{
  "type": "get_layout_suggestions",
  "components": [...],
  "layouts": [...],
  "context": {
    "app_type": "business",
    "target_audience": "professionals"
  }
}
```

**Get Component Combinations:**
```json
{
  "type": "get_component_combinations",
  "components": [...],
  "selected_component": {
    "type": "Button",
    "id": "button_1"
  },
  "context": {...}
}
```

**Analyze App Structure:**
```json
{
  "type": "analyze_app_structure",
  "components": [...],
  "layouts": [...]
}
```

#### Server-to-Client Messages

**Layout Suggestions:**
```json
{
  "type": "layout_suggestions",
  "suggestions": [
    {
      "type": "grid",
      "description": "Three-column responsive grid",
      "confidence": 0.85,
      "layout": {...}
    }
  ],
  "component_count": 5,
  "status": "success"
}
```

**Component Combinations:**
```json
{
  "type": "component_combinations",
  "suggestions": [
    {
      "components": ["Button", "Input", "Label"],
      "description": "Form input group",
      "confidence": 0.92
    }
  ],
  "selected_component": "Button",
  "status": "success"
}
```

### Notifications WebSocket

Real-time notifications for users.

#### Client-to-Server Messages

**Mark as Read:**
```json
{
  "type": "mark_as_read",
  "notification_id": "notif_123"
}
```

**Get Notifications:**
```json
{
  "type": "get_notifications",
  "limit": 20,
  "offset": 0
}
```

#### Server-to-Client Messages

**New Notification:**
```json
{
  "type": "new_notification",
  "notification": {
    "id": "notif_456",
    "type": "mention",
    "message": "You were mentioned in a comment",
    "sender": "jane_doe",
    "read": false,
    "link": "/comments/123"
  }
}
```

**Unread Count:**
```json
{
  "type": "unread_count",
  "count": 5
}
```

### Performance Monitoring WebSocket

Real-time performance metrics and monitoring.

#### Client-to-Server Messages

**Start Monitoring:**
```json
{
  "type": "start_monitoring",
  "metrics": ["cpu", "memory", "network"],
  "interval": 5000
}
```

**Stop Monitoring:**
```json
{
  "type": "stop_monitoring"
}
```

**Get System Info:**
```json
{
  "type": "get_system_info"
}
```

#### Server-to-Client Messages

**Performance Metrics:**
```json
{
  "type": "performance_metrics",
  "metrics": {
    "cpu_usage": 45.2,
    "memory_usage": 67.8,
    "network_io": {
      "bytes_sent": 1024,
      "bytes_received": 2048
    }
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**System Info:**
```json
{
  "type": "system_info",
  "info": {
    "platform": "linux",
    "cpu_count": 4,
    "total_memory": 8589934592,
    "python_version": "3.9.7"
  }
}
```

## GraphQL API

The App Builder also provides a GraphQL endpoint for flexible data querying.

### Endpoint

```
POST /api/graphql/
Content-Type: application/json
Authorization: Bearer <token>
```

### GraphiQL Interface

Access the interactive GraphQL explorer at:
```
GET /api/graphql/
```

### Example Queries

**Get Apps:**
```graphql
query GetApps($first: Int, $after: String) {
  allApps(first: $first, after: $after) {
    edges {
      node {
        id
        name
        description
        createdAt
        updatedAt
        user {
          username
          email
        }
      }
    }
    pageInfo {
      hasNextPage
      endCursor
    }
  }
}
```

**Get App Templates:**
```graphql
query GetAppTemplates($category: String) {
  allAppTemplates(appCategory: $category) {
    edges {
      node {
        id
        name
        description
        appCategory
        previewImage
        componentsJson
      }
    }
  }
}
```

### Mutations

**Create App:**
```graphql
mutation CreateApp($input: CreateAppInput!) {
  createApp(input: $input) {
    app {
      id
      name
      description
    }
    success
    errors
  }
}
```

## Security

### CORS Configuration

The API is configured with secure CORS settings:

**Allowed Origins:**
- `http://localhost:3000` (development)
- `http://127.0.0.1:3000` (development)
- Production domains (configured via environment variables)

**Allowed Methods:**
- GET, POST, PUT, PATCH, DELETE, OPTIONS

**Allowed Headers:**
- Authorization, Content-Type, X-CSRFToken, X-Requested-With

### Security Headers

All API responses include security headers:

```http
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
```

### Input Validation

All API endpoints implement comprehensive input validation:
- JSON schema validation
- SQL injection prevention
- XSS protection through input sanitization
- File upload restrictions

### Rate Limiting

Rate limiting is implemented to prevent abuse:
- Per-IP rate limiting
- Per-user rate limiting for authenticated endpoints
- Exponential backoff for repeated violations

## Development vs Production

### Development Environment

**Base URL:** `http://localhost:8000/api/`

**WebSocket URL:** `ws://localhost:8000/ws/`

**Features:**
- Debug mode enabled
- Detailed error messages
- CORS allows localhost origins
- SQLite database (default)

### Production Environment

**Base URL:** `https://your-domain.com/api/`

**WebSocket URL:** `wss://your-domain.com/ws/`

**Features:**
- Debug mode disabled
- Generic error messages
- Strict CORS configuration
- PostgreSQL database
- SSL/TLS encryption
- Security headers enforced

### Environment Variables

Key environment variables for configuration:

```bash
# Database
DATABASE_URL=postgresql://user:pass@localhost/dbname

# Security
SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-secret

# CORS
DJANGO_CORS_ALLOWED_ORIGINS=https://your-domain.com

# Features
OPENAI_API_KEY=your-openai-key
REDIS_URL=redis://localhost:6379

# Email
EMAIL_HOST=smtp.your-provider.com
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-password
```
## Integration Examples

### Frontend JavaScript Integration

#### Basic API Client

```javascript
class AppBuilderAPI {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
        ...options.headers
      },
      ...options
    };

    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return response.json();
  }

  // App management
  async getApps() {
    return this.request('/api/v1/apps/');
  }

  async createApp(appData) {
    return this.request('/api/v1/apps/', {
      method: 'POST',
      body: JSON.stringify(appData)
    });
  }

  async updateApp(id, appData) {
    return this.request(`/api/v1/apps/${id}/`, {
      method: 'PUT',
      body: JSON.stringify(appData)
    });
  }
}

// Usage
const api = new AppBuilderAPI('http://localhost:8000', 'your-jwt-token');

// Create a new app
const newApp = await api.createApp({
  name: 'My New App',
  description: 'A sample application',
  app_data: {
    components: [],
    layouts: []
  }
});
```

#### WebSocket Integration

```javascript
class AppBuilderWebSocket {
  constructor(url, token) {
    this.url = url;
    this.token = token;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect() {
    const wsUrl = `${this.url}?token=${this.token}`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
    };

    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleMessage(message);
    };

    this.ws.onclose = () => {
      console.log('WebSocket disconnected');
      this.reconnect();
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  handleMessage(message) {
    switch (message.type) {
      case 'app_data':
        this.onAppData(message.data);
        break;
      case 'error':
        this.onError(message);
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }

  onAppData(data) {
    // Handle app data updates
    console.log('Received app data:', data);
  }

  onError(error) {
    console.error('WebSocket error:', error.message);
  }

  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, 1000 * this.reconnectAttempts);
    }
  }
}

// Usage
const ws = new AppBuilderWebSocket('ws://localhost:8000/ws/app_builder/', 'your-jwt-token');
ws.connect();

// Request app data
ws.send({
  type: 'request_app_data',
  app_id: 123
});
```
### Python Integration

```python
import requests
import websocket
import json
import threading

class AppBuilderClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.token = token
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        })

    def get_apps(self):
        response = self.session.get(f'{self.base_url}/api/v1/apps/')
        response.raise_for_status()
        return response.json()

    def create_app(self, app_data):
        response = self.session.post(
            f'{self.base_url}/api/v1/apps/',
            json=app_data
        )
        response.raise_for_status()
        return response.json()

    def get_templates(self, template_type='component'):
        endpoint = f'/api/v1/{template_type}-templates/'
        response = self.session.get(f'{self.base_url}{endpoint}')
        response.raise_for_status()
        return response.json()

# Usage
client = AppBuilderClient('http://localhost:8000', 'your-jwt-token')

# Get all apps
apps = client.get_apps()
print(f"Found {apps['count']} apps")

# Create a new app
new_app = client.create_app({
    'name': 'Python Created App',
    'description': 'Created via Python client',
    'app_data': {
        'components': [],
        'layouts': []
    }
})
```

### cURL Examples

#### Authentication

```bash
# Get CSRF token
curl -X GET http://localhost:8000/api/csrf-token/

# Login with JWT
curl -X POST http://localhost:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"username": "your_username", "password": "your_password"}'

# Register new user
curl -X POST http://localhost:8000/api/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "new_user",
    "email": "<EMAIL>",
    "password": "secure_password"
  }'
```

#### App Management

```bash
# Get apps
curl -X GET http://localhost:8000/api/v1/apps/ \
  -H "Authorization: Bearer your-jwt-token"

# Create app
curl -X POST http://localhost:8000/api/v1/apps/ \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "cURL Created App",
    "description": "Created via cURL",
    "app_data": {
      "components": [],
      "layouts": []
    }
  }'

# Update app
curl -X PUT http://localhost:8000/api/v1/apps/1/ \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated App Name",
    "description": "Updated description"
  }'
```

#### Template Management

```bash
# Get component templates
curl -X GET http://localhost:8000/api/v1/component-templates/ \
  -H "Authorization: Bearer your-jwt-token"

# Create component template
curl -X POST http://localhost:8000/api/v1/component-templates/ \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Custom Button",
    "description": "A reusable button component",
    "component_type": "Button",
    "default_props": {
      "text": "Click Me",
      "variant": "primary"
    }
  }'
```

## Common API Usage Patterns

This section provides comprehensive examples for common API usage patterns and real-world scenarios that developers encounter when building applications with the App Builder API.

### Complete Application Workflow Examples

#### End-to-End App Creation Workflow

**JavaScript Implementation:**

```javascript
class AppBuilderWorkflow {
  constructor(baseURL) {
    this.baseURL = baseURL;
    this.token = null;
    this.currentApp = null;
  }

  // Step 1: Authenticate user
  async authenticate(username, password) {
    try {
      const response = await fetch(`${this.baseURL}/api/auth/login/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password })
      });

      if (!response.ok) {
        throw new Error(`Authentication failed: ${response.status}`);
      }

      const data = await response.json();
      this.token = data.token;

      console.log('✅ Authentication successful');
      return data;
    } catch (error) {
      console.error('❌ Authentication failed:', error.message);
      throw error;
    }
  }

  // Step 2: Create new app
  async createApp(appName, description = '') {
    try {
      const response = await fetch(`${this.baseURL}/api/v1/apps/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify({
          name: appName,
          description: description,
          app_data: {
            components: [],
            layouts: [],
            styles: {},
            data: {}
          },
          is_public: false
        })
      });

      if (!response.ok) {
        throw new Error(`App creation failed: ${response.status}`);
      }

      this.currentApp = await response.json();
      console.log(`✅ App "${appName}" created with ID: ${this.currentApp.id}`);
      return this.currentApp;
    } catch (error) {
      console.error('❌ App creation failed:', error.message);
      throw error;
    }
  }

  // Step 3: Add components to app
  async addComponents(components) {
    try {
      const appData = JSON.parse(this.currentApp.app_data);

      // Add new components
      components.forEach(component => {
        appData.components.push({
          id: `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: component.type,
          props: component.props,
          position: component.position || { x: 0, y: 0 },
          size: component.size || { width: 200, height: 100 }
        });
      });

      // Update app with new components
      const response = await fetch(`${this.baseURL}/api/v1/apps/${this.currentApp.id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify({
          ...this.currentApp,
          app_data: appData
        })
      });

      if (!response.ok) {
        throw new Error(`Component addition failed: ${response.status}`);
      }

      this.currentApp = await response.json();
      console.log(`✅ Added ${components.length} components to app`);
      return this.currentApp;
    } catch (error) {
      console.error('❌ Component addition failed:', error.message);
      throw error;
    }
  }

  // Step 4: Create layout for components
  async createLayout(layoutConfig) {
    try {
      const appData = JSON.parse(this.currentApp.app_data);

      // Add layout
      appData.layouts.push({
        id: `layout_${Date.now()}`,
        type: layoutConfig.type,
        components: layoutConfig.componentIds,
        styles: layoutConfig.styles,
        responsive: layoutConfig.responsive || true
      });

      // Update app
      const response = await fetch(`${this.baseURL}/api/v1/apps/${this.currentApp.id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify({
          ...this.currentApp,
          app_data: appData
        })
      });

      if (!response.ok) {
        throw new Error(`Layout creation failed: ${response.status}`);
      }

      this.currentApp = await response.json();
      console.log('✅ Layout created successfully');
      return this.currentApp;
    } catch (error) {
      console.error('❌ Layout creation failed:', error.message);
      throw error;
    }
  }

  // Step 5: Publish app (make it public)
  async publishApp() {
    try {
      const response = await fetch(`${this.baseURL}/api/v1/apps/${this.currentApp.id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify({
          ...this.currentApp,
          is_public: true
        })
      });

      if (!response.ok) {
        throw new Error(`App publishing failed: ${response.status}`);
      }

      this.currentApp = await response.json();
      console.log('✅ App published successfully');
      return this.currentApp;
    } catch (error) {
      console.error('❌ App publishing failed:', error.message);
      throw error;
    }
  }
}

// Usage Example
async function createCompleteApp() {
  const workflow = new AppBuilderWorkflow('http://localhost:8000');

  try {
    // Step 1: Authenticate
    await workflow.authenticate('john_doe', 'secure_password');

    // Step 2: Create app
    await workflow.createApp('My Business Landing Page', 'A professional landing page for my business');

    // Step 3: Add components
    await workflow.addComponents([
      {
        type: 'Header',
        props: {
          title: 'Welcome to My Business',
          logo: '/assets/logo.png'
        },
        position: { x: 0, y: 0 }
      },
      {
        type: 'Hero',
        props: {
          headline: 'Transform Your Business Today',
          subtitle: 'Professional solutions for modern challenges',
          ctaText: 'Get Started',
          backgroundImage: '/assets/hero-bg.jpg'
        },
        position: { x: 0, y: 100 }
      },
      {
        type: 'FeatureGrid',
        props: {
          features: [
            { title: 'Fast', description: 'Lightning-fast performance' },
            { title: 'Secure', description: 'Enterprise-grade security' },
            { title: 'Scalable', description: 'Grows with your business' }
          ]
        },
        position: { x: 0, y: 500 }
      }
    ]);

    // Step 4: Create responsive layout
    await workflow.createLayout({
      type: 'flex',
      componentIds: ['component_1', 'component_2', 'component_3'],
      styles: {
        flexDirection: 'column',
        gap: '2rem',
        padding: '1rem'
      },
      responsive: true
    });

    // Step 5: Publish app
    await workflow.publishApp();

    console.log('🎉 Complete app creation workflow finished!');
    console.log('App URL:', `${workflow.baseURL}/apps/${workflow.currentApp.id}`);

  } catch (error) {
    console.error('💥 Workflow failed:', error.message);
  }
}

// Execute the workflow
createCompleteApp();
```
#### Template-Based App Creation

**JavaScript Implementation:**

```javascript
class TemplateBasedAppCreator {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }

  // Discover available app templates
  async discoverTemplates(category = null, searchTerm = null) {
    try {
      let url = `${this.baseURL}/api/v1/app-templates/`;
      const params = new URLSearchParams();

      if (category) params.append('app_category', category);
      if (searchTerm) params.append('search', searchTerm);

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Template discovery failed: ${response.status}`);
      }

      const data = await response.json();
      console.log(`✅ Found ${data.results.length} templates`);
      return data.results;
    } catch (error) {
      console.error('❌ Template discovery failed:', error.message);
      throw error;
    }
  }

  // Create app from template
  async createFromTemplate(templateId, customizations = {}) {
    try {
      // First, get the template details
      const templateResponse = await fetch(`${this.baseURL}/api/v1/app-templates/${templateId}/`, {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      if (!templateResponse.ok) {
        throw new Error(`Template fetch failed: ${templateResponse.status}`);
      }

      const template = await templateResponse.json();

      // Merge template data with customizations
      const appData = {
        name: customizations.name || `${template.name} - Copy`,
        description: customizations.description || template.description,
        app_data: {
          ...template.components_json,
          ...customizations.appData
        },
        is_public: customizations.isPublic || false
      };

      // Apply customizations to components
      if (customizations.componentOverrides) {
        this.applyComponentOverrides(appData.app_data, customizations.componentOverrides);
      }

      // Create the app
      const appResponse = await fetch(`${this.baseURL}/api/v1/apps/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify(appData)
      });

      if (!appResponse.ok) {
        throw new Error(`App creation failed: ${appResponse.status}`);
      }

      const newApp = await appResponse.json();
      console.log(`✅ App created from template: ${newApp.id}`);
      return newApp;
    } catch (error) {
      console.error('❌ Template-based app creation failed:', error.message);
      throw error;
    }
  }

  // Apply component customizations
  applyComponentOverrides(appData, overrides) {
    if (appData.components) {
      appData.components.forEach(component => {
        const override = overrides[component.type];
        if (override) {
          component.props = { ...component.props, ...override };
        }
      });
    }
  }

  // Get featured templates
  async getFeaturedTemplates() {
    try {
      const response = await fetch(`${this.baseURL}/api/featured-templates/`, {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Featured templates fetch failed: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Featured templates loaded');
      return data;
    } catch (error) {
      console.error('❌ Featured templates fetch failed:', error.message);
      throw error;
    }
  }
}

// Usage Example
async function createBusinessLandingPage() {
  const creator = new TemplateBasedAppCreator('http://localhost:8000', 'your-jwt-token');

  try {
    // Discover business templates
    const templates = await creator.discoverTemplates('business', 'landing');

    if (templates.length === 0) {
      throw new Error('No business landing page templates found');
    }

    // Select the first template
    const selectedTemplate = templates[0];
    console.log(`Using template: ${selectedTemplate.name}`);

    // Create app with customizations
    const newApp = await creator.createFromTemplate(selectedTemplate.id, {
      name: 'Acme Corp Landing Page',
      description: 'Professional landing page for Acme Corporation',
      componentOverrides: {
        'Header': {
          title: 'Acme Corporation',
          logo: '/assets/acme-logo.png'
        },
        'Hero': {
          headline: 'Innovation at Its Finest',
          subtitle: 'Leading the industry with cutting-edge solutions',
          ctaText: 'Learn More'
        },
        'ContactForm': {
          email: '<EMAIL>',
          phone: '******-0123'
        }
      },
      isPublic: true
    });

    console.log('🎉 Business landing page created successfully!');
    console.log('App ID:', newApp.id);
    return newApp;

  } catch (error) {
    console.error('💥 Template-based creation failed:', error.message);
  }
}

createBusinessLandingPage();
```
### Real-time Collaboration Patterns

#### Multi-User Editing Session Setup

**JavaScript Implementation:**

```javascript
class CollaborationManager {
  constructor(baseURL, token, sessionId) {
    this.baseURL = baseURL;
    this.token = token;
    this.sessionId = sessionId;
    this.ws = null;
    this.participants = new Map();
    this.cursors = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.messageQueue = [];
    this.isConnected = false;
  }

  // Initialize collaboration session
  async initializeSession(userInfo) {
    try {
      this.userInfo = userInfo;
      await this.connectWebSocket();
      this.setupEventHandlers();

      // Join the session
      this.sendMessage({
        type: 'join_session',
        session_id: this.sessionId,
        user_info: userInfo
      });

      console.log(`✅ Collaboration session initialized: ${this.sessionId}`);
    } catch (error) {
      console.error('❌ Session initialization failed:', error.message);
      throw error;
    }
  }

  // Connect to collaboration WebSocket
  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      const wsUrl = `ws://${this.baseURL.replace('http://', '')}/ws/collaboration/${this.sessionId}/?token=${this.token}`;
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('🔗 WebSocket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;

        // Send queued messages
        this.flushMessageQueue();
        resolve();
      };

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('❌ Message parsing failed:', error);
        }
      };

      this.ws.onclose = (event) => {
        console.log('🔌 WebSocket disconnected:', event.code);
        this.isConnected = false;
        this.handleReconnection();
      };

      this.ws.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
        reject(error);
      };

      // Connection timeout
      setTimeout(() => {
        if (!this.isConnected) {
          reject(new Error('WebSocket connection timeout'));
        }
      }, 10000);
    });
  }

  // Handle incoming messages
  handleMessage(message) {
    switch (message.type) {
      case 'user_joined':
        this.handleUserJoined(message);
        break;
      case 'user_left':
        this.handleUserLeft(message);
        break;
      case 'cursor_broadcast':
        this.handleCursorUpdate(message);
        break;
      case 'edit_broadcast':
        this.handleEditBroadcast(message);
        break;
      case 'chat_message':
        this.handleChatMessage(message);
        break;
      case 'comment_created':
        this.handleCommentCreated(message);
        break;
      case 'error':
        console.error('❌ Collaboration error:', message.message);
        break;
      default:
        console.log('📨 Unknown message type:', message.type);
    }
  }

  // Handle user joining session
  handleUserJoined(message) {
    const user = message.user;
    this.participants.set(user.id, user);

    console.log(`👋 User joined: ${user.username}`);

    // Trigger UI update
    this.onUserJoined?.(user, Array.from(this.participants.values()));
  }

  // Handle user leaving session
  handleUserLeft(message) {
    const userId = message.user_id;
    const user = this.participants.get(userId);

    if (user) {
      this.participants.delete(userId);
      this.cursors.delete(userId);

      console.log(`👋 User left: ${user.username}`);

      // Trigger UI update
      this.onUserLeft?.(user, Array.from(this.participants.values()));
    }
  }

  // Send cursor position updates
  updateCursor(position, componentId = null) {
    const message = {
      type: 'cursor_update',
      position: position,
      component_id: componentId,
      timestamp: Date.now()
    };

    this.sendMessage(message);
  }

  // Handle cursor updates from other users
  handleCursorUpdate(message) {
    if (message.user_id !== this.userInfo.id) {
      this.cursors.set(message.user_id, {
        userId: message.user_id,
        username: message.username,
        position: message.position,
        componentId: message.component_id,
        timestamp: message.timestamp
      });

      // Trigger UI update
      this.onCursorUpdate?.(message);
    }
  }

  // Send component edit operations
  sendEdit(operation) {
    const message = {
      type: 'edit_operation',
      operation: {
        ...operation,
        timestamp: Date.now(),
        user_id: this.userInfo.id
      }
    };

    this.sendMessage(message);
  }

  // Handle edit broadcasts from other users
  handleEditBroadcast(message) {
    if (message.user_id !== this.userInfo.id) {
      console.log(`📝 Edit from ${message.username}:`, message.operation);

      // Apply the edit with conflict resolution
      this.applyRemoteEdit(message.operation);

      // Trigger UI update
      this.onEditReceived?.(message);
    }
  }

  // Apply remote edits with conflict resolution
  applyRemoteEdit(operation) {
    try {
      // Implement operational transformation or conflict resolution
      switch (operation.type) {
        case 'component_update':
          this.applyComponentUpdate(operation);
          break;
        case 'component_add':
          this.applyComponentAdd(operation);
          break;
        case 'component_delete':
          this.applyComponentDelete(operation);
          break;
        case 'component_move':
          this.applyComponentMove(operation);
          break;
        default:
          console.warn('Unknown operation type:', operation.type);
      }
    } catch (error) {
      console.error('❌ Failed to apply remote edit:', error);
    }
  }

  // Send chat message
  sendChatMessage(message) {
    this.sendMessage({
      type: 'chat_message',
      message: message
    });
  }

  // Handle chat messages
  handleChatMessage(message) {
    console.log(`💬 ${message.username}: ${message.message}`);
    this.onChatMessage?.(message);
  }

  // Create comment on component
  createComment(content, componentId, canvasPosition) {
    this.sendMessage({
      type: 'create_comment',
      content: content,
      component_id: componentId,
      canvas_position: canvasPosition
    });
  }

  // Handle comment creation
  handleCommentCreated(message) {
    console.log('💭 New comment created:', message.comment);
    this.onCommentCreated?.(message.comment);
  }

  // Send message with queuing for offline scenarios
  sendMessage(message) {
    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      // Queue message for when connection is restored
      this.messageQueue.push(message);
      console.log('📤 Message queued (offline):', message.type);
    }
  }

  // Flush queued messages
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.ws.send(JSON.stringify(message));
      console.log('📤 Sent queued message:', message.type);
    }
  }

  // Handle reconnection with exponential backoff
  handleReconnection() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

      console.log(`🔄 Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

      setTimeout(async () => {
        try {
          await this.connectWebSocket();

          // Rejoin session after reconnection
          this.sendMessage({
            type: 'join_session',
            session_id: this.sessionId,
            user_info: this.userInfo
          });
        } catch (error) {
          console.error('❌ Reconnection failed:', error);
        }
      }, delay);
    } else {
      console.error('💥 Max reconnection attempts reached');
      this.onConnectionLost?.();
    }
  }

  // Cleanup and disconnect
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.isConnected = false;
    }

    this.participants.clear();
    this.cursors.clear();
    this.messageQueue = [];

    console.log('🔌 Collaboration session disconnected');
  }

  // Event handler setups
  setupEventHandlers() {
    // These can be overridden by the implementing application
    this.onUserJoined = null;
    this.onUserLeft = null;
    this.onCursorUpdate = null;
    this.onEditReceived = null;
    this.onChatMessage = null;
    this.onCommentCreated = null;
    this.onConnectionLost = null;
  }
}

// Usage Example
async function setupCollaborativeEditing() {
  const collaboration = new CollaborationManager(
    'http://localhost:8000',
    'your-jwt-token',
    'session_123'
  );

  // Set up event handlers
  collaboration.onUserJoined = (user, participants) => {
    console.log(`User ${user.username} joined. Total participants: ${participants.length}`);
    updateParticipantsList(participants);
  };

  collaboration.onCursorUpdate = (cursorData) => {
    updateCursorDisplay(cursorData);
  };

  collaboration.onEditReceived = (editData) => {
    applyEditToCanvas(editData.operation);
  };

  collaboration.onChatMessage = (message) => {
    addChatMessage(message);
  };

  // Initialize session
  await collaboration.initializeSession({
    id: 'user_123',
    username: 'john_doe',
    avatar: '/avatars/john.jpg',
    color: '#3498db'
  });

  // Example: Send cursor updates on mouse move
  document.addEventListener('mousemove', (event) => {
    collaboration.updateCursor({
      x: event.clientX,
      y: event.clientY
    });
  });

  // Example: Send edit when component is modified
  function onComponentUpdate(componentId, newProps) {
    collaboration.sendEdit({
      type: 'component_update',
      component_id: componentId,
      new_props: newProps,
      timestamp: Date.now()
    });
  }

  return collaboration;
}

setupCollaborativeEditing();
```
### Template Management Workflows

#### Creating and Publishing Component Templates

**JavaScript Implementation:**

```javascript
class TemplateManager {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }

  // Create a new component template
  async createComponentTemplate(templateData) {
    try {
      // Validate template data
      this.validateComponentTemplate(templateData);

      const response = await fetch(`${this.baseURL}/api/v1/component-templates/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify(templateData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Template creation failed: ${errorData.message || response.status}`);
      }

      const template = await response.json();
      console.log(`✅ Component template created: ${template.id}`);
      return template;
    } catch (error) {
      console.error('❌ Component template creation failed:', error.message);
      throw error;
    }
  }

  // Create a complex layout template with multiple slots
  async createLayoutTemplate(layoutData) {
    try {
      // Validate layout structure
      this.validateLayoutTemplate(layoutData);

      const response = await fetch(`${this.baseURL}/api/v1/layout-templates/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify(layoutData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Layout template creation failed: ${errorData.message || response.status}`);
      }

      const template = await response.json();
      console.log(`✅ Layout template created: ${template.id}`);
      return template;
    } catch (error) {
      console.error('❌ Layout template creation failed:', error.message);
      throw error;
    }
  }

  // Publish template (make it public)
  async publishTemplate(templateId, templateType = 'component') {
    try {
      const endpoint = `${this.baseURL}/api/v1/${templateType}-templates/${templateId}/`;

      const response = await fetch(endpoint, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify({
          is_public: true
        })
      });

      if (!response.ok) {
        throw new Error(`Template publishing failed: ${response.status}`);
      }

      const template = await response.json();
      console.log(`✅ Template published: ${template.id}`);
      return template;
    } catch (error) {
      console.error('❌ Template publishing failed:', error.message);
      throw error;
    }
  }

  // Search and filter templates
  async searchTemplates(templateType, filters = {}) {
    try {
      const params = new URLSearchParams();

      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          params.append(key, value);
        }
      });

      const url = `${this.baseURL}/api/v1/${templateType}-templates/?${params.toString()}`;

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Template search failed: ${response.status}`);
      }

      const data = await response.json();
      console.log(`✅ Found ${data.results.length} templates`);
      return data;
    } catch (error) {
      console.error('❌ Template search failed:', error.message);
      throw error;
    }
  }

  // Create template version (for versioning)
  async createTemplateVersion(templateId, templateType, versionData) {
    try {
      // Get current template
      const currentTemplate = await this.getTemplate(templateId, templateType);

      // Create new version with updated data
      const versionedTemplate = {
        ...currentTemplate,
        name: `${currentTemplate.name} v${versionData.version}`,
        description: versionData.description || currentTemplate.description,
        default_props: { ...currentTemplate.default_props, ...versionData.updates },
        version: versionData.version,
        parent_template_id: templateId
      };

      // Remove ID to create new template
      delete versionedTemplate.id;
      delete versionedTemplate.created_at;

      const response = await fetch(`${this.baseURL}/api/v1/${templateType}-templates/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify(versionedTemplate)
      });

      if (!response.ok) {
        throw new Error(`Template versioning failed: ${response.status}`);
      }

      const newVersion = await response.json();
      console.log(`✅ Template version created: ${newVersion.id}`);
      return newVersion;
    } catch (error) {
      console.error('❌ Template versioning failed:', error.message);
      throw error;
    }
  }

  // Get template details
  async getTemplate(templateId, templateType) {
    try {
      const response = await fetch(`${this.baseURL}/api/v1/${templateType}-templates/${templateId}/`, {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Template fetch failed: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('❌ Template fetch failed:', error.message);
      throw error;
    }
  }

  // Validation methods
  validateComponentTemplate(templateData) {
    const required = ['name', 'component_type', 'default_props'];
    const missing = required.filter(field => !templateData[field]);

    if (missing.length > 0) {
      throw new Error(`Missing required fields: ${missing.join(', ')}`);
    }

    if (typeof templateData.default_props !== 'object') {
      throw new Error('default_props must be an object');
    }
  }

  validateLayoutTemplate(layoutData) {
    const required = ['name', 'layout_type', 'components'];
    const missing = required.filter(field => !layoutData[field]);

    if (missing.length > 0) {
      throw new Error(`Missing required fields: ${missing.join(', ')}`);
    }

    if (typeof layoutData.components !== 'object') {
      throw new Error('components must be an object');
    }
  }
}

// Usage Examples
async function createButtonTemplate() {
  const templateManager = new TemplateManager('http://localhost:8000', 'your-jwt-token');

  try {
    // Create a reusable button component template
    const buttonTemplate = await templateManager.createComponentTemplate({
      name: 'Primary Action Button',
      description: 'A primary action button with consistent styling',
      component_type: 'Button',
      default_props: {
        text: 'Click Me',
        variant: 'primary',
        size: 'medium',
        disabled: false,
        loading: false,
        icon: null,
        onClick: null,
        style: {
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          padding: '8px 16px',
          fontSize: '14px',
          fontWeight: '500',
          cursor: 'pointer'
        }
      },
      is_public: false
    });

    // Publish the template
    await templateManager.publishTemplate(buttonTemplate.id, 'component');

    console.log('🎉 Button template created and published!');
    return buttonTemplate;
  } catch (error) {
    console.error('💥 Button template creation failed:', error.message);
  }
}

async function createDashboardLayout() {
  const templateManager = new TemplateManager('http://localhost:8000', 'your-jwt-token');

  try {
    // Create a complex dashboard layout template
    const dashboardLayout = await templateManager.createLayoutTemplate({
      name: 'Business Dashboard Layout',
      description: 'A responsive dashboard layout with header, sidebar, and main content area',
      layout_type: 'dashboard',
      components: {
        slots: {
          header: {
            type: 'header',
            required: true,
            allowedComponents: ['Header', 'Navigation', 'Breadcrumb']
          },
          sidebar: {
            type: 'sidebar',
            required: false,
            allowedComponents: ['Menu', 'Navigation', 'UserProfile']
          },
          main: {
            type: 'main',
            required: true,
            allowedComponents: ['*'] // Allow any component
          },
          footer: {
            type: 'footer',
            required: false,
            allowedComponents: ['Footer', 'Copyright']
          }
        },
        structure: {
          type: 'grid',
          areas: [
            ['header', 'header'],
            ['sidebar', 'main'],
            ['footer', 'footer']
          ],
          columns: '250px 1fr',
          rows: 'auto 1fr auto'
        }
      },
      default_props: {
        responsive: true,
        breakpoints: {
          mobile: {
            columns: '1fr',
            areas: [
              ['header'],
              ['main'],
              ['sidebar'],
              ['footer']
            ]
          },
          tablet: {
            columns: '200px 1fr',
            areas: [
              ['header', 'header'],
              ['sidebar', 'main'],
              ['footer', 'footer']
            ]
          }
        },
        spacing: {
          gap: '1rem',
          padding: '1rem'
        }
      },
      is_public: false
    });

    // Publish the layout template
    await templateManager.publishTemplate(dashboardLayout.id, 'layout');

    console.log('🎉 Dashboard layout template created and published!');
    return dashboardLayout;
  } catch (error) {
    console.error('💥 Dashboard layout creation failed:', error.message);
  }
}

// Template discovery and filtering example
async function discoverTemplates() {
  const templateManager = new TemplateManager('http://localhost:8000', 'your-jwt-token');

  try {
    // Search for public button components
    const buttonTemplates = await templateManager.searchTemplates('component', {
      component_type: 'Button',
      is_public: true,
      search: 'primary'
    });

    console.log('Button templates found:', buttonTemplates.results.length);

    // Search for dashboard layouts
    const dashboardLayouts = await templateManager.searchTemplates('layout', {
      layout_type: 'dashboard',
      is_public: true
    });

    console.log('Dashboard layouts found:', dashboardLayouts.results.length);

    return {
      buttons: buttonTemplates.results,
      dashboards: dashboardLayouts.results
    };
  } catch (error) {
    console.error('💥 Template discovery failed:', error.message);
  }
}

// Execute examples
createButtonTemplate();
createDashboardLayout();
discoverTemplates();
```
### AI-Assisted Development Patterns

#### Real-time AI Suggestions Integration

**JavaScript Implementation:**

```javascript
class AIAssistant {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
    this.aiWebSocket = null;
    this.isConnected = false;
    this.suggestionCache = new Map();
    this.requestQueue = [];
  }

  // Initialize AI WebSocket connection
  async initializeAIConnection() {
    try {
      const wsUrl = `ws://${this.baseURL.replace('http://', '')}/ws/ai-suggestions/?token=${this.token}`;
      this.aiWebSocket = new WebSocket(wsUrl);

      this.aiWebSocket.onopen = () => {
        console.log('🤖 AI WebSocket connected');
        this.isConnected = true;
        this.processRequestQueue();
      };

      this.aiWebSocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleAIMessage(message);
        } catch (error) {
          console.error('❌ AI message parsing failed:', error);
        }
      };

      this.aiWebSocket.onclose = () => {
        console.log('🤖 AI WebSocket disconnected');
        this.isConnected = false;
      };

      this.aiWebSocket.onerror = (error) => {
        console.error('❌ AI WebSocket error:', error);
      };

    } catch (error) {
      console.error('❌ AI connection initialization failed:', error);
      throw error;
    }
  }

  // Request layout suggestions based on existing components
  async requestLayoutSuggestions(components, layouts = [], context = {}) {
    try {
      const requestId = `layout_${Date.now()}`;
      const request = {
        type: 'get_layout_suggestions',
        request_id: requestId,
        components: components,
        layouts: layouts,
        context: {
          app_type: context.appType || 'general',
          target_audience: context.targetAudience || 'general',
          design_style: context.designStyle || 'modern',
          responsive: context.responsive !== false,
          ...context
        }
      };

      return new Promise((resolve, reject) => {
        // Store resolver for this request
        this.pendingRequests = this.pendingRequests || new Map();
        this.pendingRequests.set(requestId, { resolve, reject, timestamp: Date.now() });

        // Send request
        if (this.isConnected) {
          this.aiWebSocket.send(JSON.stringify(request));
        } else {
          this.requestQueue.push(request);
          console.log('🤖 AI request queued (offline)');
        }

        // Set timeout for request
        setTimeout(() => {
          if (this.pendingRequests.has(requestId)) {
            this.pendingRequests.delete(requestId);
            reject(new Error('AI request timeout'));
          }
        }, 30000); // 30 second timeout
      });
    } catch (error) {
      console.error('❌ Layout suggestion request failed:', error);
      throw error;
    }
  }

  // Request component combination suggestions
  async requestComponentCombinations(components, selectedComponent = null, context = {}) {
    try {
      const requestId = `combinations_${Date.now()}`;
      const request = {
        type: 'get_component_combinations',
        request_id: requestId,
        components: components,
        selected_component: selectedComponent,
        context: context
      };

      return new Promise((resolve, reject) => {
        this.pendingRequests = this.pendingRequests || new Map();
        this.pendingRequests.set(requestId, { resolve, reject, timestamp: Date.now() });

        if (this.isConnected) {
          this.aiWebSocket.send(JSON.stringify(request));
        } else {
          this.requestQueue.push(request);
        }

        setTimeout(() => {
          if (this.pendingRequests.has(requestId)) {
            this.pendingRequests.delete(requestId);
            reject(new Error('AI request timeout'));
          }
        }, 30000);
      });
    } catch (error) {
      console.error('❌ Component combination request failed:', error);
      throw error;
    }
  }

  // Analyze app structure for optimization suggestions
  async analyzeAppStructure(components, layouts) {
    try {
      const requestId = `analysis_${Date.now()}`;
      const request = {
        type: 'analyze_app_structure',
        request_id: requestId,
        components: components,
        layouts: layouts
      };

      return new Promise((resolve, reject) => {
        this.pendingRequests = this.pendingRequests || new Map();
        this.pendingRequests.set(requestId, { resolve, reject, timestamp: Date.now() });

        if (this.isConnected) {
          this.aiWebSocket.send(JSON.stringify(request));
        } else {
          this.requestQueue.push(request);
        }

        setTimeout(() => {
          if (this.pendingRequests.has(requestId)) {
            this.pendingRequests.delete(requestId);
            reject(new Error('AI request timeout'));
          }
        }, 30000);
      });
    } catch (error) {
      console.error('❌ App analysis request failed:', error);
      throw error;
    }
  }

  // Handle AI WebSocket messages
  handleAIMessage(message) {
    const { type, request_id } = message;

    // Handle responses to pending requests
    if (request_id && this.pendingRequests && this.pendingRequests.has(request_id)) {
      const { resolve } = this.pendingRequests.get(request_id);
      this.pendingRequests.delete(request_id);

      switch (type) {
        case 'layout_suggestions':
          this.handleLayoutSuggestions(message, resolve);
          break;
        case 'component_combinations':
          this.handleComponentCombinations(message, resolve);
          break;
        case 'app_analysis':
          this.handleAppAnalysis(message, resolve);
          break;
        case 'error':
          console.error('❌ AI error:', message.message);
          resolve({ error: message.message, suggestions: [] });
          break;
      }
    } else {
      // Handle broadcast messages
      switch (type) {
        case 'ai_suggestion_update':
          this.onSuggestionUpdate?.(message);
          break;
        default:
          console.log('🤖 Unknown AI message type:', type);
      }
    }
  }

  // Handle layout suggestions response
  handleLayoutSuggestions(message, resolve) {
    const suggestions = message.suggestions || [];

    // Cache suggestions for quick access
    const cacheKey = `layout_${JSON.stringify(message.components)}`;
    this.suggestionCache.set(cacheKey, {
      suggestions,
      timestamp: Date.now(),
      ttl: 300000 // 5 minutes
    });

    console.log(`🤖 Received ${suggestions.length} layout suggestions`);

    // Process and enhance suggestions
    const enhancedSuggestions = suggestions.map(suggestion => ({
      ...suggestion,
      id: `layout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      preview: this.generateLayoutPreview(suggestion),
      applicability: this.calculateApplicability(suggestion, message.components)
    }));

    resolve({
      suggestions: enhancedSuggestions,
      metadata: {
        component_count: message.component_count,
        confidence: this.calculateAverageConfidence(enhancedSuggestions),
        timestamp: message.timestamp
      }
    });

    // Trigger UI update
    this.onLayoutSuggestions?.(enhancedSuggestions);
  }

  // Handle component combinations response
  handleComponentCombinations(message, resolve) {
    const suggestions = message.suggestions || [];

    console.log(`🤖 Received ${suggestions.length} component combinations`);

    const enhancedSuggestions = suggestions.map(suggestion => ({
      ...suggestion,
      id: `combo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      preview: this.generateCombinationPreview(suggestion),
      compatibility: this.checkComponentCompatibility(suggestion.components)
    }));

    resolve({
      suggestions: enhancedSuggestions,
      metadata: {
        selected_component: message.selected_component,
        timestamp: message.timestamp
      }
    });

    this.onComponentCombinations?.(enhancedSuggestions);
  }

  // Handle app analysis response
  handleAppAnalysis(message, resolve) {
    const analysis = message.analysis || {};

    console.log('🤖 Received app structure analysis');

    const enhancedAnalysis = {
      ...analysis,
      recommendations: this.prioritizeRecommendations(analysis.recommendations || []),
      performance_score: this.calculatePerformanceScore(analysis),
      accessibility_score: this.calculateAccessibilityScore(analysis)
    };

    resolve(enhancedAnalysis);
    this.onAppAnalysis?.(enhancedAnalysis);
  }

  // Generate layout preview data
  generateLayoutPreview(suggestion) {
    return {
      type: suggestion.type,
      thumbnail: `/api/generate-layout-preview/`,
      description: suggestion.description,
      responsive: suggestion.responsive || false,
      complexity: this.calculateLayoutComplexity(suggestion)
    };
  }

  // Generate combination preview
  generateCombinationPreview(suggestion) {
    return {
      components: suggestion.components,
      description: suggestion.description,
      use_cases: suggestion.use_cases || [],
      difficulty: suggestion.difficulty || 'medium'
    };
  }

  // Calculate suggestion applicability
  calculateApplicability(suggestion, components) {
    // Simple heuristic based on component types and suggestion requirements
    const componentTypes = components.map(c => c.type);
    const requiredTypes = suggestion.required_components || [];

    const matches = requiredTypes.filter(type => componentTypes.includes(type));
    return matches.length / Math.max(requiredTypes.length, 1);
  }

  // Calculate average confidence
  calculateAverageConfidence(suggestions) {
    if (suggestions.length === 0) return 0;
    const total = suggestions.reduce((sum, s) => sum + (s.confidence || 0), 0);
    return total / suggestions.length;
  }

  // Check component compatibility
  checkComponentCompatibility(components) {
    // Implement compatibility checking logic
    return {
      compatible: true,
      warnings: [],
      suggestions: []
    };
  }

  // Process queued requests when connection is restored
  processRequestQueue() {
    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift();
      this.aiWebSocket.send(JSON.stringify(request));
      console.log('🤖 Sent queued AI request:', request.type);
    }
  }

  // HTTP fallback for AI suggestions (when WebSocket is unavailable)
  async getAISuggestionsHTTP(components, layouts, context) {
    try {
      const response = await fetch(`${this.baseURL}/api/generate_ai_suggestions/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify({
          components,
          layouts,
          context
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP AI request failed: ${response.status}`);
      }

      const data = await response.json();
      console.log('🤖 HTTP AI suggestions received');
      return data;
    } catch (error) {
      console.error('❌ HTTP AI request failed:', error);
      throw error;
    }
  }

  // Cleanup and disconnect
  disconnect() {
    if (this.aiWebSocket) {
      this.aiWebSocket.close();
      this.isConnected = false;
    }

    this.suggestionCache.clear();
    this.requestQueue = [];

    if (this.pendingRequests) {
      this.pendingRequests.clear();
    }
  }

  // Event handlers (to be overridden by implementing application)
  onLayoutSuggestions = null;
  onComponentCombinations = null;
  onAppAnalysis = null;
  onSuggestionUpdate = null;
}

// Usage Example
async function setupAIAssistant() {
  const aiAssistant = new AIAssistant('http://localhost:8000', 'your-jwt-token');

  // Set up event handlers
  aiAssistant.onLayoutSuggestions = (suggestions) => {
    console.log('📐 Layout suggestions received:', suggestions.length);
    displayLayoutSuggestions(suggestions);
  };

  aiAssistant.onComponentCombinations = (combinations) => {
    console.log('🧩 Component combinations received:', combinations.length);
    displayComponentCombinations(combinations);
  };

  aiAssistant.onAppAnalysis = (analysis) => {
    console.log('📊 App analysis received');
    displayAnalysisResults(analysis);
  };

  // Initialize AI connection
  await aiAssistant.initializeAIConnection();

  // Example: Request layout suggestions for current components
  const currentComponents = [
    { type: 'Header', props: { title: 'My App' } },
    { type: 'Button', props: { text: 'Click Me' } },
    { type: 'Input', props: { placeholder: 'Enter text' } }
  ];

  try {
    const layoutSuggestions = await aiAssistant.requestLayoutSuggestions(
      currentComponents,
      [],
      {
        appType: 'business',
        targetAudience: 'professionals',
        designStyle: 'modern',
        responsive: true
      }
    );

    console.log('🎯 Layout suggestions:', layoutSuggestions);

    // Request component combinations for the button
    const combinations = await aiAssistant.requestComponentCombinations(
      currentComponents,
      { type: 'Button', id: 'button_1' },
      { purpose: 'form_submission' }
    );

    console.log('🔗 Component combinations:', combinations);

    // Analyze current app structure
    const analysis = await aiAssistant.analyzeAppStructure(
      currentComponents,
      []
    );

    console.log('🔍 App analysis:', analysis);

  } catch (error) {
    console.error('💥 AI assistant error:', error);
  }

  return aiAssistant;
}

// Helper functions for UI integration
function displayLayoutSuggestions(suggestions) {
  // Implement UI logic to display layout suggestions
  suggestions.forEach(suggestion => {
    console.log(`Layout: ${suggestion.description} (confidence: ${suggestion.confidence})`);
  });
}

function displayComponentCombinations(combinations) {
  // Implement UI logic to display component combinations
  combinations.forEach(combo => {
    console.log(`Combination: ${combo.description} (${combo.components.join(', ')})`);
  });
}

function displayAnalysisResults(analysis) {
  // Implement UI logic to display analysis results
  console.log(`Performance Score: ${analysis.performance_score}`);
  console.log(`Accessibility Score: ${analysis.accessibility_score}`);
  console.log(`Recommendations: ${analysis.recommendations.length}`);
}

setupAIAssistant();
```
### Error Handling and Resilience Patterns

#### Comprehensive Error Handling with Retry Logic

**JavaScript Implementation:**

```javascript
class ResilientAPIClient {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
    this.retryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffFactor: 2,
      jitter: true
    };
    this.rateLimitConfig = {
      windowMs: 60000, // 1 minute
      maxRequests: 100,
      requestLog: []
    };
  }

  // Enhanced fetch with comprehensive error handling
  async resilientFetch(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    let lastError;

    for (let attempt = 0; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        // Check rate limiting before making request
        await this.checkRateLimit();

        // Add authentication and default headers
        const config = {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.token}`,
            ...options.headers
          },
          ...options
        };

        console.log(`🔄 API Request (attempt ${attempt + 1}): ${options.method || 'GET'} ${endpoint}`);

        const response = await fetch(url, config);

        // Handle different response scenarios
        if (response.ok) {
          console.log(`✅ API Success: ${response.status} ${endpoint}`);
          return await this.parseResponse(response);
        }

        // Handle specific error cases
        const errorData = await this.parseErrorResponse(response);

        if (response.status === 401) {
          throw new AuthenticationError('Authentication failed', errorData);
        } else if (response.status === 403) {
          throw new AuthorizationError('Insufficient permissions', errorData);
        } else if (response.status === 404) {
          throw new NotFoundError('Resource not found', errorData);
        } else if (response.status === 422) {
          throw new ValidationError('Validation failed', errorData);
        } else if (response.status === 429) {
          // Rate limit exceeded - handle with exponential backoff
          const retryAfter = this.getRetryAfter(response);
          throw new RateLimitError('Rate limit exceeded', { retryAfter, ...errorData });
        } else if (response.status >= 500) {
          // Server error - retry with backoff
          throw new ServerError('Server error', errorData);
        } else {
          // Client error - don't retry
          throw new ClientError('Client error', errorData);
        }

      } catch (error) {
        lastError = error;

        // Don't retry for certain error types
        if (error instanceof AuthenticationError ||
            error instanceof AuthorizationError ||
            error instanceof NotFoundError ||
            error instanceof ValidationError ||
            error instanceof ClientError) {
          throw error;
        }

        // Calculate delay for retry
        if (attempt < this.retryConfig.maxRetries) {
          const delay = this.calculateRetryDelay(attempt, error);
          console.log(`⏳ Retrying in ${delay}ms (attempt ${attempt + 1}/${this.retryConfig.maxRetries})`);
          await this.sleep(delay);
        }
      }
    }

    // All retries exhausted
    console.error(`💥 API request failed after ${this.retryConfig.maxRetries + 1} attempts`);
    throw lastError;
  }

  // Parse successful response
  async parseResponse(response) {
    const contentType = response.headers.get('content-type');

    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    } else if (contentType && contentType.includes('text/')) {
      return await response.text();
    } else {
      return await response.blob();
    }
  }

  // Parse error response
  async parseErrorResponse(response) {
    try {
      const errorData = await response.json();
      return {
        status: response.status,
        statusText: response.statusText,
        ...errorData
      };
    } catch (parseError) {
      return {
        status: response.status,
        statusText: response.statusText,
        message: 'Failed to parse error response'
      };
    }
  }

  // Calculate retry delay with exponential backoff and jitter
  calculateRetryDelay(attempt, error) {
    let delay = this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffFactor, attempt);

    // Handle rate limit specific delays
    if (error instanceof RateLimitError && error.retryAfter) {
      delay = Math.max(delay, error.retryAfter * 1000);
    }

    // Apply maximum delay cap
    delay = Math.min(delay, this.retryConfig.maxDelay);

    // Add jitter to prevent thundering herd
    if (this.retryConfig.jitter) {
      const jitterRange = delay * 0.1; // 10% jitter
      const jitter = (Math.random() - 0.5) * 2 * jitterRange;
      delay += jitter;
    }

    return Math.max(delay, 0);
  }

  // Get retry-after header value
  getRetryAfter(response) {
    const retryAfter = response.headers.get('retry-after');
    if (retryAfter) {
      const seconds = parseInt(retryAfter, 10);
      return isNaN(seconds) ? 60 : seconds; // Default to 60 seconds if invalid
    }
    return 60; // Default retry after 60 seconds
  }

  // Rate limiting check
  async checkRateLimit() {
    const now = Date.now();
    const windowStart = now - this.rateLimitConfig.windowMs;

    // Clean old requests from log
    this.rateLimitConfig.requestLog = this.rateLimitConfig.requestLog.filter(
      timestamp => timestamp > windowStart
    );

    // Check if we're within rate limit
    if (this.rateLimitConfig.requestLog.length >= this.rateLimitConfig.maxRequests) {
      const oldestRequest = Math.min(...this.rateLimitConfig.requestLog);
      const waitTime = oldestRequest + this.rateLimitConfig.windowMs - now;

      if (waitTime > 0) {
        console.log(`⏳ Rate limit reached, waiting ${waitTime}ms`);
        await this.sleep(waitTime);
      }
    }

    // Log this request
    this.rateLimitConfig.requestLog.push(now);
  }

  // Sleep utility
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Validation error processing
  processValidationErrors(errorData) {
    const errors = errorData.error?.details || errorData.details || {};
    const processedErrors = {};

    Object.entries(errors).forEach(([field, messages]) => {
      processedErrors[field] = Array.isArray(messages) ? messages : [messages];
    });

    return {
      message: errorData.error?.message || 'Validation failed',
      fields: processedErrors,
      code: errorData.error?.code || 'VALIDATION_ERROR'
    };
  }

  // User-friendly error messages
  getUserFriendlyMessage(error) {
    if (error instanceof AuthenticationError) {
      return 'Please log in to continue.';
    } else if (error instanceof AuthorizationError) {
      return 'You don\'t have permission to perform this action.';
    } else if (error instanceof NotFoundError) {
      return 'The requested resource was not found.';
    } else if (error instanceof ValidationError) {
      const validation = this.processValidationErrors(error.data);
      return `Please fix the following errors: ${Object.keys(validation.fields).join(', ')}`;
    } else if (error instanceof RateLimitError) {
      return 'Too many requests. Please wait a moment and try again.';
    } else if (error instanceof ServerError) {
      return 'Server is temporarily unavailable. Please try again later.';
    } else if (error instanceof NetworkError) {
      return 'Network connection failed. Please check your internet connection.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }
}

// Custom Error Classes
class APIError extends Error {
  constructor(message, data = {}) {
    super(message);
    this.name = this.constructor.name;
    this.data = data;
    this.timestamp = new Date().toISOString();
  }
}

class AuthenticationError extends APIError {}
class AuthorizationError extends APIError {}
class NotFoundError extends APIError {}
class ValidationError extends APIError {}
class RateLimitError extends APIError {
  constructor(message, data = {}) {
    super(message, data);
    this.retryAfter = data.retryAfter;
  }
}
class ServerError extends APIError {}
class ClientError extends APIError {}
class NetworkError extends APIError {}

// WebSocket Resilience Manager
class ResilientWebSocket {
  constructor(url, token, options = {}) {
    this.url = url;
    this.token = token;
    this.options = {
      maxReconnectAttempts: 10,
      reconnectInterval: 1000,
      maxReconnectInterval: 30000,
      reconnectDecay: 1.5,
      timeoutInterval: 2000,
      enableHeartbeat: true,
      heartbeatInterval: 30000,
      ...options
    };

    this.ws = null;
    this.reconnectAttempts = 0;
    this.reconnectTimer = null;
    this.heartbeatTimer = null;
    this.messageQueue = [];
    this.isConnected = false;
    this.isReconnecting = false;
  }

  // Connect with resilience
  async connect() {
    try {
      this.cleanup();

      const wsUrl = `${this.url}?token=${this.token}`;
      this.ws = new WebSocket(wsUrl);

      // Set up event handlers
      this.ws.onopen = () => this.handleOpen();
      this.ws.onmessage = (event) => this.handleMessage(event);
      this.ws.onclose = (event) => this.handleClose(event);
      this.ws.onerror = (error) => this.handleError(error);

      // Connection timeout
      const timeout = setTimeout(() => {
        if (this.ws.readyState === WebSocket.CONNECTING) {
          this.ws.close();
          this.handleError(new Error('Connection timeout'));
        }
      }, this.options.timeoutInterval);

      // Wait for connection
      return new Promise((resolve, reject) => {
        this.connectResolve = resolve;
        this.connectReject = reject;

        // Clear timeout on connection
        this.ws.addEventListener('open', () => clearTimeout(timeout));
        this.ws.addEventListener('error', () => clearTimeout(timeout));
      });

    } catch (error) {
      console.error('❌ WebSocket connection failed:', error);
      throw error;
    }
  }

  // Handle connection open
  handleOpen() {
    console.log('🔗 WebSocket connected');
    this.isConnected = true;
    this.isReconnecting = false;
    this.reconnectAttempts = 0;

    // Start heartbeat
    if (this.options.enableHeartbeat) {
      this.startHeartbeat();
    }

    // Send queued messages
    this.flushMessageQueue();

    // Resolve connection promise
    if (this.connectResolve) {
      this.connectResolve();
      this.connectResolve = null;
    }

    // Trigger event
    this.onOpen?.();
  }

  // Handle incoming messages
  handleMessage(event) {
    try {
      const message = JSON.parse(event.data);

      // Handle heartbeat responses
      if (message.type === 'pong') {
        this.lastPongReceived = Date.now();
        return;
      }

      // Trigger message handler
      this.onMessage?.(message);
    } catch (error) {
      console.error('❌ WebSocket message parsing failed:', error);
    }
  }

  // Handle connection close
  handleClose(event) {
    console.log(`🔌 WebSocket closed: ${event.code} - ${event.reason}`);
    this.isConnected = false;
    this.cleanup();

    // Trigger event
    this.onClose?.(event);

    // Attempt reconnection if not intentional
    if (!event.wasClean && !this.isReconnecting) {
      this.scheduleReconnect();
    }
  }

  // Handle errors
  handleError(error) {
    console.error('❌ WebSocket error:', error);

    // Reject connection promise if pending
    if (this.connectReject) {
      this.connectReject(error);
      this.connectReject = null;
    }

    // Trigger event
    this.onError?.(error);
  }

  // Schedule reconnection with exponential backoff
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      console.error('💥 Max reconnection attempts reached');
      this.onMaxReconnectAttemptsReached?.();
      return;
    }

    this.isReconnecting = true;
    this.reconnectAttempts++;

    const delay = Math.min(
      this.options.reconnectInterval * Math.pow(this.options.reconnectDecay, this.reconnectAttempts - 1),
      this.options.maxReconnectInterval
    );

    console.log(`🔄 Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`);

    this.reconnectTimer = setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        console.error('❌ Reconnection failed:', error);
        this.scheduleReconnect();
      }
    }, delay);
  }

  // Send message with queuing
  send(message) {
    const messageStr = typeof message === 'string' ? message : JSON.stringify(message);

    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(messageStr);
    } else {
      // Queue message for when connection is restored
      this.messageQueue.push(messageStr);
      console.log('📤 Message queued (offline)');
    }
  }

  // Flush queued messages
  flushMessageQueue() {
    while (this.messageQueue.length > 0 && this.isConnected) {
      const message = this.messageQueue.shift();
      this.ws.send(message);
      console.log('📤 Sent queued message');
    }
  }

  // Start heartbeat mechanism
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping', timestamp: Date.now() });

        // Check if we received a pong recently
        if (this.lastPongReceived && Date.now() - this.lastPongReceived > this.options.heartbeatInterval * 2) {
          console.warn('⚠️ Heartbeat timeout, closing connection');
          this.ws.close();
        }
      }
    }, this.options.heartbeatInterval);
  }

  // Cleanup resources
  cleanup() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  // Close connection
  close() {
    this.cleanup();
    this.isReconnecting = false;

    if (this.ws) {
      this.ws.close();
    }
  }

  // Event handlers (to be overridden)
  onOpen = null;
  onMessage = null;
  onClose = null;
  onError = null;
  onMaxReconnectAttemptsReached = null;
}

// Usage Example
async function setupResilientAPI() {
  const apiClient = new ResilientAPIClient('http://localhost:8000', 'your-jwt-token');

  try {
    // Example: Create app with error handling
    const newApp = await apiClient.resilientFetch('/api/v1/apps/', {
      method: 'POST',
      body: JSON.stringify({
        name: 'Test App',
        description: 'Testing resilient API',
        app_data: { components: [], layouts: [] }
      })
    });

    console.log('✅ App created:', newApp.id);

    // Example: Handle validation errors
    try {
      await apiClient.resilientFetch('/api/v1/apps/', {
        method: 'POST',
        body: JSON.stringify({
          // Missing required fields to trigger validation error
          description: 'Invalid app data'
        })
      });
    } catch (error) {
      if (error instanceof ValidationError) {
        const validation = apiClient.processValidationErrors(error.data);
        console.log('Validation errors:', validation.fields);

        // Show user-friendly message
        console.log('User message:', apiClient.getUserFriendlyMessage(error));
      }
    }

  } catch (error) {
    console.error('API Error:', error.message);
    console.log('User-friendly message:', apiClient.getUserFriendlyMessage(error));
  }

  // Setup resilient WebSocket
  const ws = new ResilientWebSocket('ws://localhost:8000/ws/app_builder/', 'your-jwt-token');

  ws.onOpen = () => console.log('WebSocket ready');
  ws.onMessage = (message) => console.log('Received:', message);
  ws.onError = (error) => console.error('WebSocket error:', error);
  ws.onMaxReconnectAttemptsReached = () => {
    console.error('WebSocket connection permanently lost');
    // Notify user and provide manual reconnect option
  };

  await ws.connect();

  return { apiClient, ws };
}

setupResilientAPI();
```
### Performance Optimization Patterns

#### Efficient Data Loading and Caching

**JavaScript Implementation:**

```javascript
class PerformanceOptimizedClient {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
    this.cache = new Map();
    this.requestCache = new Map(); // For deduplicating concurrent requests
    this.batchQueue = new Map();
    this.batchTimer = null;
    this.batchDelay = 100; // ms
  }

  // Cached API requests with TTL
  async cachedRequest(endpoint, options = {}, ttl = 300000) { // 5 minutes default TTL
    const cacheKey = this.generateCacheKey(endpoint, options);
    const cached = this.cache.get(cacheKey);

    // Return cached data if valid
    if (cached && Date.now() - cached.timestamp < ttl) {
      console.log(`📦 Cache hit: ${endpoint}`);
      return cached.data;
    }

    // Check for concurrent request to same endpoint
    if (this.requestCache.has(cacheKey)) {
      console.log(`⏳ Waiting for concurrent request: ${endpoint}`);
      return await this.requestCache.get(cacheKey);
    }

    // Make request and cache promise
    const requestPromise = this.makeRequest(endpoint, options);
    this.requestCache.set(cacheKey, requestPromise);

    try {
      const data = await requestPromise;

      // Cache successful response
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });

      console.log(`💾 Cached response: ${endpoint}`);
      return data;
    } finally {
      // Remove from request cache
      this.requestCache.delete(cacheKey);
    }
  }

  // Paginated data loading with intelligent prefetching
  async loadPaginatedData(endpoint, options = {}) {
    const {
      page = 1,
      pageSize = 20,
      prefetchPages = 2,
      filters = {},
      sort = null
    } = options;

    const results = [];
    const loadedPages = new Set();

    // Load initial page
    const initialData = await this.loadPage(endpoint, page, pageSize, filters, sort);
    results.push(...initialData.results);
    loadedPages.add(page);

    // Prefetch adjacent pages in background
    const prefetchPromises = [];
    for (let i = 1; i <= prefetchPages; i++) {
      const nextPage = page + i;
      const prevPage = page - i;

      if (nextPage <= Math.ceil(initialData.count / pageSize)) {
        prefetchPromises.push(
          this.loadPage(endpoint, nextPage, pageSize, filters, sort)
            .then(data => ({ page: nextPage, data }))
            .catch(error => ({ page: nextPage, error }))
        );
      }

      if (prevPage >= 1) {
        prefetchPromises.push(
          this.loadPage(endpoint, prevPage, pageSize, filters, sort)
            .then(data => ({ page: prevPage, data }))
            .catch(error => ({ page: prevPage, error }))
        );
      }
    }

    // Wait for prefetch to complete (but don't block)
    Promise.all(prefetchPromises).then(prefetchResults => {
      prefetchResults.forEach(result => {
        if (!result.error) {
          console.log(`🚀 Prefetched page ${result.page}`);
        }
      });
    });

    return {
      results,
      totalCount: initialData.count,
      currentPage: page,
      totalPages: Math.ceil(initialData.count / pageSize),
      hasNext: initialData.next !== null,
      hasPrevious: initialData.previous !== null,
      loadedPages: Array.from(loadedPages)
    };
  }

  // Load specific page with caching
  async loadPage(endpoint, page, pageSize, filters, sort) {
    const params = new URLSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
      ...filters
    });

    if (sort) {
      params.append('ordering', sort);
    }

    const url = `${endpoint}?${params.toString()}`;
    return await this.cachedRequest(url, { method: 'GET' });
  }

  // Batch multiple requests to reduce network overhead
  async batchRequest(requests) {
    const batchId = Date.now().toString();

    // Add requests to batch queue
    requests.forEach((request, index) => {
      const requestId = `${batchId}_${index}`;
      this.batchQueue.set(requestId, {
        ...request,
        resolve: null,
        reject: null,
        promise: null
      });
    });

    // Create promises for each request
    const promises = requests.map((request, index) => {
      const requestId = `${batchId}_${index}`;
      return new Promise((resolve, reject) => {
        const queuedRequest = this.batchQueue.get(requestId);
        queuedRequest.resolve = resolve;
        queuedRequest.reject = reject;
      });
    });

    // Schedule batch processing
    this.scheduleBatchProcessing();

    return Promise.all(promises);
  }

  // Schedule batch processing with debouncing
  scheduleBatchProcessing() {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
    }

    this.batchTimer = setTimeout(() => {
      this.processBatch();
    }, this.batchDelay);
  }

  // Process batched requests
  async processBatch() {
    if (this.batchQueue.size === 0) return;

    const requests = Array.from(this.batchQueue.values());
    this.batchQueue.clear();

    console.log(`📦 Processing batch of ${requests.length} requests`);

    // Group requests by endpoint for optimization
    const groupedRequests = this.groupRequestsByEndpoint(requests);

    // Process each group
    for (const [endpoint, endpointRequests] of groupedRequests) {
      try {
        if (endpointRequests.length === 1) {
          // Single request
          const request = endpointRequests[0];
          const result = await this.makeRequest(request.endpoint, request.options);
          request.resolve(result);
        } else {
          // Multiple requests to same endpoint - can be optimized
          await this.processGroupedRequests(endpoint, endpointRequests);
        }
      } catch (error) {
        // Reject all requests in this group
        endpointRequests.forEach(request => request.reject(error));
      }
    }
  }

  // Group requests by endpoint
  groupRequestsByEndpoint(requests) {
    const groups = new Map();

    requests.forEach(request => {
      const endpoint = request.endpoint;
      if (!groups.has(endpoint)) {
        groups.set(endpoint, []);
      }
      groups.get(endpoint).push(request);
    });

    return groups;
  }

  // Process multiple requests to the same endpoint
  async processGroupedRequests(endpoint, requests) {
    // For GET requests, we can potentially combine them
    const getRequests = requests.filter(r => !r.options.method || r.options.method === 'GET');
    const otherRequests = requests.filter(r => r.options.method && r.options.method !== 'GET');

    // Process GET requests (potentially combinable)
    if (getRequests.length > 0) {
      await this.processCombinableGETRequests(endpoint, getRequests);
    }

    // Process other requests individually
    for (const request of otherRequests) {
      try {
        const result = await this.makeRequest(request.endpoint, request.options);
        request.resolve(result);
      } catch (error) {
        request.reject(error);
      }
    }
  }

  // Process GET requests that can potentially be combined
  async processCombinableGETRequests(endpoint, requests) {
    // For now, process individually, but this could be optimized
    // to combine multiple ID-based requests into a single bulk request
    for (const request of requests) {
      try {
        const result = await this.cachedRequest(request.endpoint, request.options);
        request.resolve(result);
      } catch (error) {
        request.reject(error);
      }
    }
  }

  // Bulk operations for multiple updates
  async bulkUpdate(endpoint, updates) {
    const batchSize = 50; // Process in batches to avoid overwhelming server
    const results = [];

    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);

      console.log(`🔄 Processing bulk update batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(updates.length / batchSize)}`);

      try {
        const batchResult = await this.makeRequest(`${endpoint}/bulk/`, {
          method: 'POST',
          body: JSON.stringify({ updates: batch })
        });

        results.push(...batchResult.results);

        // Invalidate cache for affected items
        batch.forEach(update => {
          this.invalidateCacheForItem(endpoint, update.id);
        });

      } catch (error) {
        console.error(`❌ Bulk update batch failed:`, error);
        throw error;
      }

      // Small delay between batches to be nice to the server
      if (i + batchSize < updates.length) {
        await this.sleep(100);
      }
    }

    return results;
  }

  // Intelligent cache invalidation
  invalidateCache(pattern) {
    const keysToDelete = [];

    for (const [key] of this.cache) {
      if (key.includes(pattern)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => {
      this.cache.delete(key);
      console.log(`🗑️ Invalidated cache: ${key}`);
    });
  }

  // Invalidate cache for specific item
  invalidateCacheForItem(endpoint, itemId) {
    this.invalidateCache(`${endpoint}/${itemId}`);
    this.invalidateCache(endpoint); // Also invalidate list endpoints
  }

  // Memory-efficient cache management
  manageCacheSize() {
    const maxCacheSize = 1000; // Maximum number of cached items

    if (this.cache.size > maxCacheSize) {
      // Remove oldest entries
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

      const toRemove = entries.slice(0, this.cache.size - maxCacheSize);
      toRemove.forEach(([key]) => {
        this.cache.delete(key);
      });

      console.log(`🧹 Cleaned ${toRemove.length} old cache entries`);
    }
  }

  // Generate cache key
  generateCacheKey(endpoint, options) {
    const method = options.method || 'GET';
    const body = options.body || '';
    const params = new URLSearchParams(options.params || {}).toString();

    return `${method}:${endpoint}:${params}:${this.hashString(body)}`;
  }

  // Simple string hash function
  hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  // Make actual HTTP request
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
        ...options.headers
      },
      ...options
    };

    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  // Utility sleep function
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Cleanup resources
  cleanup() {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
    }

    this.cache.clear();
    this.requestCache.clear();
    this.batchQueue.clear();
  }
}

// Usage Examples
async function demonstratePerformanceOptimizations() {
  const client = new PerformanceOptimizedClient('http://localhost:8000', 'your-jwt-token');

  try {
    // Example 1: Efficient paginated loading with prefetching
    console.log('📄 Loading paginated apps with prefetching...');
    const appsData = await client.loadPaginatedData('/api/v1/apps/', {
      page: 1,
      pageSize: 20,
      prefetchPages: 2,
      filters: { is_public: 'true' },
      sort: '-created_at'
    });

    console.log(`Loaded ${appsData.results.length} apps (${appsData.totalCount} total)`);

    // Example 2: Cached requests
    console.log('💾 Making cached requests...');
    const app1 = await client.cachedRequest('/api/v1/apps/1/');
    const app1Again = await client.cachedRequest('/api/v1/apps/1/'); // Will use cache

    // Example 3: Batch requests
    console.log('📦 Making batch requests...');
    const batchRequests = [
      { endpoint: '/api/v1/apps/1/', options: { method: 'GET' } },
      { endpoint: '/api/v1/apps/2/', options: { method: 'GET' } },
      { endpoint: '/api/v1/component-templates/', options: { method: 'GET' } }
    ];

    const batchResults = await client.batchRequest(batchRequests);
    console.log(`Batch completed: ${batchResults.length} results`);

    // Example 4: Bulk updates
    console.log('🔄 Performing bulk updates...');
    const updates = [
      { id: 1, name: 'Updated App 1' },
      { id: 2, name: 'Updated App 2' },
      { id: 3, name: 'Updated App 3' }
    ];

    const bulkResults = await client.bulkUpdate('/api/v1/apps', updates);
    console.log(`Bulk update completed: ${bulkResults.length} items updated`);

    // Example 5: Cache management
    console.log('🧹 Managing cache...');
    client.manageCacheSize();
    client.invalidateCache('/api/v1/apps/');

  } catch (error) {
    console.error('💥 Performance optimization demo failed:', error);
  } finally {
    client.cleanup();
  }
}

// WebSocket Message Throttling and Queuing
class ThrottledWebSocket {
  constructor(url, token, options = {}) {
    this.url = url;
    this.token = token;
    this.options = {
      messageThrottleMs: 100,
      maxQueueSize: 1000,
      batchSize: 10,
      ...options
    };

    this.ws = null;
    this.messageQueue = [];
    this.throttleTimer = null;
    this.isConnected = false;
  }

  async connect() {
    const wsUrl = `${this.url}?token=${this.token}`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log('🔗 Throttled WebSocket connected');
      this.isConnected = true;
      this.startMessageProcessing();
    };

    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.onMessage?.(message);
    };

    this.ws.onclose = () => {
      console.log('🔌 Throttled WebSocket disconnected');
      this.isConnected = false;
      this.stopMessageProcessing();
    };
  }

  // Send message with throttling
  send(message) {
    if (this.messageQueue.length >= this.options.maxQueueSize) {
      console.warn('⚠️ Message queue full, dropping oldest message');
      this.messageQueue.shift();
    }

    this.messageQueue.push(message);
  }

  // Start processing queued messages
  startMessageProcessing() {
    this.throttleTimer = setInterval(() => {
      this.processMessageQueue();
    }, this.options.messageThrottleMs);
  }

  // Stop processing messages
  stopMessageProcessing() {
    if (this.throttleTimer) {
      clearInterval(this.throttleTimer);
      this.throttleTimer = null;
    }
  }

  // Process queued messages in batches
  processMessageQueue() {
    if (!this.isConnected || this.messageQueue.length === 0) {
      return;
    }

    const batch = this.messageQueue.splice(0, this.options.batchSize);

    batch.forEach(message => {
      const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
      this.ws.send(messageStr);
    });

    if (batch.length > 0) {
      console.log(`📤 Sent batch of ${batch.length} messages`);
    }
  }

  close() {
    this.stopMessageProcessing();
    if (this.ws) {
      this.ws.close();
    }
  }

  onMessage = null;
}

demonstratePerformanceOptimizations();
```
### Authentication Flow Examples

#### Complete JWT Authentication Workflow

**JavaScript Implementation:**

```javascript
class AuthenticationManager {
  constructor(baseURL) {
    this.baseURL = baseURL;
    this.token = null;
    this.refreshToken = null;
    this.user = null;
    this.tokenExpiryTime = null;
    this.refreshTimer = null;
  }

  // Complete registration workflow
  async register(userData) {
    try {
      const response = await fetch(`${this.baseURL}/api/auth/register/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: userData.username,
          email: userData.email,
          password: userData.password,
          first_name: userData.firstName || '',
          last_name: userData.lastName || ''
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new AuthenticationError('Registration failed', errorData);
      }

      const data = await response.json();

      // Store authentication data
      this.setAuthenticationData(data);

      console.log('✅ Registration successful');
      return {
        user: this.user,
        token: this.token,
        success: true
      };
    } catch (error) {
      console.error('❌ Registration failed:', error.message);
      throw error;
    }
  }

  // Login with automatic token refresh setup
  async login(username, password) {
    try {
      const response = await fetch(`${this.baseURL}/api/auth/login/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new AuthenticationError('Login failed', errorData);
      }

      const data = await response.json();

      // Store authentication data
      this.setAuthenticationData(data);

      // Setup automatic token refresh
      this.setupTokenRefresh();

      console.log('✅ Login successful');
      return {
        user: this.user,
        token: this.token,
        success: true
      };
    } catch (error) {
      console.error('❌ Login failed:', error.message);
      throw error;
    }
  }

  // Set authentication data and persist to storage
  setAuthenticationData(data) {
    this.token = data.token;
    this.refreshToken = data.refresh_token;
    this.user = data.user;
    this.tokenExpiryTime = data.expires_at ? new Date(data.expires_at) : null;

    // Persist to localStorage
    this.persistAuthData();
  }

  // Setup automatic token refresh
  setupTokenRefresh() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    if (!this.tokenExpiryTime) return;

    // Refresh token 5 minutes before expiry
    const refreshTime = this.tokenExpiryTime.getTime() - Date.now() - (5 * 60 * 1000);

    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(() => {
        this.refreshAuthToken();
      }, refreshTime);

      console.log(`🔄 Token refresh scheduled in ${Math.round(refreshTime / 1000)}s`);
    }
  }

  // Refresh authentication token
  async refreshAuthToken() {
    try {
      if (!this.refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await fetch(`${this.baseURL}/api/auth/refresh/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refresh: this.refreshToken
        })
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const data = await response.json();

      // Update token data
      this.token = data.access;
      if (data.refresh) {
        this.refreshToken = data.refresh;
      }
      this.tokenExpiryTime = data.expires_at ? new Date(data.expires_at) : null;

      // Persist updated data
      this.persistAuthData();

      // Setup next refresh
      this.setupTokenRefresh();

      console.log('✅ Token refreshed successfully');

      // Notify listeners
      this.onTokenRefreshed?.(this.token);

    } catch (error) {
      console.error('❌ Token refresh failed:', error.message);

      // Clear invalid tokens and redirect to login
      this.logout();
      this.onAuthenticationExpired?.();
    }
  }

  // Get user profile
  async getUserProfile() {
    try {
      if (!this.token) {
        throw new Error('No authentication token');
      }

      const response = await fetch(`${this.baseURL}/api/auth/profile/`, {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user profile');
      }

      const userData = await response.json();
      this.user = userData;

      // Update persisted data
      this.persistAuthData();

      return userData;
    } catch (error) {
      console.error('❌ Failed to get user profile:', error.message);
      throw error;
    }
  }

  // Update user profile
  async updateProfile(updates) {
    try {
      if (!this.token) {
        throw new Error('No authentication token');
      }

      const response = await fetch(`${this.baseURL}/api/auth/profile/update/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new ValidationError('Profile update failed', errorData);
      }

      const updatedUser = await response.json();
      this.user = updatedUser;

      // Update persisted data
      this.persistAuthData();

      console.log('✅ Profile updated successfully');
      return updatedUser;
    } catch (error) {
      console.error('❌ Profile update failed:', error.message);
      throw error;
    }
  }

  // Logout and cleanup
  logout() {
    // Clear timers
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }

    // Clear authentication data
    this.token = null;
    this.refreshToken = null;
    this.user = null;
    this.tokenExpiryTime = null;

    // Clear persisted data
    this.clearPersistedAuthData();

    console.log('✅ Logged out successfully');

    // Notify listeners
    this.onLogout?.();
  }

  // Check if user is authenticated
  isAuthenticated() {
    return this.token !== null && (!this.tokenExpiryTime || this.tokenExpiryTime > new Date());
  }

  // Get current authentication token
  getToken() {
    if (!this.isAuthenticated()) {
      return null;
    }
    return this.token;
  }

  // Persist authentication data to localStorage
  persistAuthData() {
    const authData = {
      token: this.token,
      refreshToken: this.refreshToken,
      user: this.user,
      tokenExpiryTime: this.tokenExpiryTime?.toISOString()
    };

    localStorage.setItem('app_builder_auth', JSON.stringify(authData));
  }

  // Load authentication data from localStorage
  loadPersistedAuthData() {
    try {
      const authDataStr = localStorage.getItem('app_builder_auth');
      if (!authDataStr) return false;

      const authData = JSON.parse(authDataStr);

      this.token = authData.token;
      this.refreshToken = authData.refreshToken;
      this.user = authData.user;
      this.tokenExpiryTime = authData.tokenExpiryTime ? new Date(authData.tokenExpiryTime) : null;

      // Check if token is still valid
      if (this.isAuthenticated()) {
        this.setupTokenRefresh();
        console.log('✅ Authentication restored from storage');
        return true;
      } else {
        this.clearPersistedAuthData();
        return false;
      }
    } catch (error) {
      console.error('❌ Failed to load persisted auth data:', error);
      this.clearPersistedAuthData();
      return false;
    }
  }

  // Clear persisted authentication data
  clearPersistedAuthData() {
    localStorage.removeItem('app_builder_auth');
  }

  // Initialize authentication manager
  async initialize() {
    // Try to restore authentication from storage
    const restored = this.loadPersistedAuthData();

    if (restored) {
      try {
        // Verify token is still valid by fetching user profile
        await this.getUserProfile();
        return true;
      } catch (error) {
        console.warn('⚠️ Stored token is invalid, clearing auth data');
        this.logout();
        return false;
      }
    }

    return false;
  }

  // Event handlers (to be overridden by implementing application)
  onTokenRefreshed = null;
  onAuthenticationExpired = null;
  onLogout = null;
}

// Session-based authentication with CSRF protection
class CSRFAuthenticationManager {
  constructor(baseURL) {
    this.baseURL = baseURL;
    this.csrfToken = null;
    this.isAuthenticated = false;
  }

  // Get CSRF token
  async getCSRFToken() {
    try {
      const response = await fetch(`${this.baseURL}/api/csrf-token/`, {
        credentials: 'include' // Include cookies
      });

      if (!response.ok) {
        throw new Error('Failed to get CSRF token');
      }

      const data = await response.json();
      this.csrfToken = data.csrfToken;

      console.log('✅ CSRF token obtained');
      return this.csrfToken;
    } catch (error) {
      console.error('❌ Failed to get CSRF token:', error.message);
      throw error;
    }
  }

  // Login with CSRF protection
  async login(username, password) {
    try {
      // Get CSRF token first
      await this.getCSRFToken();

      const response = await fetch(`${this.baseURL}/api/auth/login/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.csrfToken
        },
        credentials: 'include',
        body: JSON.stringify({ username, password })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new AuthenticationError('Login failed', errorData);
      }

      const data = await response.json();
      this.isAuthenticated = true;

      console.log('✅ CSRF login successful');
      return data;
    } catch (error) {
      console.error('❌ CSRF login failed:', error.message);
      throw error;
    }
  }

  // Make authenticated request with CSRF protection
  async authenticatedRequest(endpoint, options = {}) {
    if (!this.csrfToken) {
      await this.getCSRFToken();
    }

    const config = {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': this.csrfToken,
        ...options.headers
      },
      credentials: 'include',
      ...options
    };

    const response = await fetch(`${this.baseURL}${endpoint}`, config);

    if (response.status === 403) {
      // CSRF token might be invalid, try to refresh
      await this.getCSRFToken();
      config.headers['X-CSRFToken'] = this.csrfToken;

      // Retry request
      return await fetch(`${this.baseURL}${endpoint}`, config);
    }

    return response;
  }
}

// Usage Examples
async function demonstrateAuthenticationFlows() {
  // JWT Authentication Example
  const jwtAuth = new AuthenticationManager('http://localhost:8000');

  try {
    // Initialize and check for existing authentication
    const restored = await jwtAuth.initialize();

    if (!restored) {
      // Register new user
      await jwtAuth.register({
        username: 'john_doe',
        email: '<EMAIL>',
        password: 'secure_password_123',
        firstName: 'John',
        lastName: 'Doe'
      });
    }

    // Login (if not already authenticated)
    if (!jwtAuth.isAuthenticated()) {
      await jwtAuth.login('john_doe', 'secure_password_123');
    }

    // Get user profile
    const profile = await jwtAuth.getUserProfile();
    console.log('User profile:', profile);

    // Update profile
    await jwtAuth.updateProfile({
      first_name: 'Jonathan',
      email: '<EMAIL>'
    });

    // Use token for API requests
    const token = jwtAuth.getToken();
    console.log('Current token:', token);

  } catch (error) {
    console.error('JWT authentication demo failed:', error);
  }

  // CSRF Authentication Example
  const csrfAuth = new CSRFAuthenticationManager('http://localhost:8000');

  try {
    // Login with CSRF protection
    await csrfAuth.login('john_doe', 'secure_password_123');

    // Make authenticated request
    const response = await csrfAuth.authenticatedRequest('/api/v1/apps/', {
      method: 'POST',
      body: JSON.stringify({
        name: 'CSRF Protected App',
        description: 'Created with CSRF protection'
      })
    });

    if (response.ok) {
      const newApp = await response.json();
      console.log('CSRF protected app created:', newApp.id);
    }

  } catch (error) {
    console.error('CSRF authentication demo failed:', error);
  }
}

// Error classes for authentication
class AuthenticationError extends Error {
  constructor(message, data = {}) {
    super(message);
    this.name = 'AuthenticationError';
    this.data = data;
  }
}

class ValidationError extends Error {
  constructor(message, data = {}) {
    super(message);
    this.name = 'ValidationError';
    this.data = data;
  }
}

demonstrateAuthenticationFlows();
```

---

## Summary

This comprehensive API documentation now includes detailed examples for all common usage patterns:

### 🔧 **Complete Coverage:**

1. **End-to-End Workflows**: Complete app creation, template-based development, and collaboration setup
2. **Real-time Collaboration**: Multi-user editing, cursor tracking, conflict resolution, and chat integration
3. **Template Management**: Creating, publishing, versioning, and discovering templates
4. **AI-Assisted Development**: Real-time suggestions, layout optimization, and component recommendations
5. **Error Handling & Resilience**: Comprehensive retry logic, WebSocket reconnection, and user-friendly error messages
6. **Performance Optimization**: Caching, pagination, batching, and efficient data loading
7. **Authentication Flows**: JWT and CSRF authentication with token refresh and session management

### 🎯 **Key Features:**

- **Production-Ready Code**: All examples include proper error handling, logging, and best practices
- **Multiple Languages**: JavaScript (frontend), Python (automation), and cURL (testing) examples
- **Real-World Scenarios**: Practical examples that developers will actually encounter
- **Performance Focused**: Optimized patterns for scalability and user experience
- **Security Conscious**: Proper authentication, validation, and error handling throughout

### 📚 **Developer Benefits:**

- **Copy-Paste Ready**: Examples can be directly integrated into applications
- **Educational**: Detailed comments explain implementation decisions and best practices
- **Comprehensive**: Covers both simple use cases and complex enterprise scenarios
- **Maintainable**: Structured code that's easy to understand and modify
- **Resilient**: Built-in error handling and recovery mechanisms

This documentation now serves as a complete reference for developers building applications with the App Builder API, providing both the theoretical knowledge and practical implementation examples needed for successful integration.

---

This documentation covers the complete App Builder API with comprehensive examples for all common usage patterns. For additional support or questions, please refer to the project repository or contact the development team.
