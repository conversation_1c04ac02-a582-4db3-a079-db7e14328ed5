# Enhanced Code Export Functionality

## Overview

The Enhanced Code Export functionality provides comprehensive code generation capabilities for the App Builder application. It supports multiple frameworks, languages, and export formats with advanced configuration options, quality assurance, and modern development practices.

## Features

### 🚀 Supported Export Formats

#### Frontend Frameworks
- **React** - Modern React with hooks, TypeScript support
- **React + TypeScript** - React with full TypeScript integration
- **Vue.js** - Vue 3 with Composition API and TypeScript
- **Vue + TypeScript** - Vue 3 with full TypeScript support
- **Angular** - Angular with TypeScript and modern features
- **Svelte** - Svelte with modern JavaScript/TypeScript
- **Next.js** - Next.js with SSR and modern React features
- **Nuxt.js** - Nuxt.js for Vue applications

#### Mobile Frameworks
- **React Native** - Cross-platform mobile development with TypeScript support
- **Flutter** - Google's UI toolkit for mobile, web, and desktop (Dart)
- **Ionic** - Hybrid mobile app development

#### Static Formats
- **HTML/CSS/JavaScript** - Vanilla web technologies with modern standards
- **Bootstrap Templates** - Bootstrap-based responsive templates

#### Backend APIs
- **Express.js** - Node.js web application framework with TypeScript support
- **Django REST Framework** - Python web framework with modern patterns
- **FastAPI** - Modern Python API framework with automatic documentation

### 🎨 Style Framework Support

- **Styled Components** - CSS-in-JS with styled-components (React/React Native)
- **Emotion** - CSS-in-JS with Emotion (React)
- **Tailwind CSS** - Utility-first CSS framework (All frameworks)
- **CSS Modules** - Scoped CSS with modules (React/Vue/Angular)
- **Material-UI** - React Material Design components
- **Chakra UI** - Simple and modular React components
- **Bootstrap** - Popular CSS framework (HTML/React/Vue/Angular)

### ⚙️ Advanced Configuration Options

#### Code Quality
- **TypeScript Support** - Generate TypeScript code with type definitions and interfaces
- **Accessibility Features** - Include ARIA labels, roles, semantic HTML, and accessibility attributes
- **ESLint Configuration** - Code linting and style enforcement with framework-specific rules
- **Prettier Integration** - Code formatting with consistent style

#### Testing & Documentation
- **Unit Tests** - Generate test files for components (Jest, React Testing Library, Vue Test Utils)
- **Storybook Stories** - Component documentation and testing (React/Vue/Angular)
- **README Generation** - Comprehensive project documentation with setup instructions
- **API Documentation** - Automatic API documentation generation (OpenAPI/Swagger)

#### Project Structure Options
- **Single File** - All code in one file for simple projects and prototypes
- **Multi-File** - Organized file structure with separate components and modules
- **Full Project** - Complete project with build configs, tooling, and deployment setup

#### Development Tools
- **Build Configuration** - Webpack 5, Vite, or Parcel 2 configuration with optimization
- **Package Management** - npm, yarn, or pnpm support with lock files
- **Environment Configuration** - Development and production settings with environment variables
- **Docker Support** - Containerization with Dockerfile and docker-compose for all frameworks
- **CI/CD Pipelines** - GitHub Actions, GitLab CI, and Azure DevOps templates

## Usage

### Frontend Integration

```javascript
import EnhancedCodeExporter from './components/enhanced/EnhancedCodeExporter';

// Use in your App Builder interface
<EnhancedCodeExporter
  appData={appData}
  onExportComplete={handleExportComplete}
  onExportError={handleExportError}
/>
```

### API Endpoints

#### Enhanced Export
```http
POST /api/enhanced-export/
Content-Type: application/json
Authorization: Bearer <your-token>

{
  "app_id": 1,
  "format": "react-ts",
  "options": {
    "typescript": true,
    "include_accessibility": true,
    "project_structure": "full-project",
    "style_framework": "styled-components",
    "state_management": "redux",
    "include_tests": true,
    "include_storybook": true,
    "include_docker": true,
    "include_ci_cd": true,
    "bundler": "vite",
    "package_manager": "pnpm"
  }
}
```

**Response:**
```json
{
  "status": "success",
  "type": "project",
  "files": {
    "src/App.tsx": "...",
    "src/components/Button.tsx": "...",
    "package.json": "...",
    "README.md": "..."
  },
  "metadata": {
    "format": "react-ts",
    "generated_at": "2024-01-01T12:00:00Z",
    "app_name": "My App"
  }
}
```

#### Template Export
```http
POST /api/export-template/
Content-Type: application/json
Authorization: Bearer <your-token>

{
  "template_id": 1,
  "template_type": "layout",
  "format": "vue-ts",
  "options": {
    "typescript": true,
    "project_structure": "full-project",
    "style_framework": "tailwind"
  }
}
```

#### Batch Export
```http
POST /api/batch-export/
Content-Type: application/json
Authorization: Bearer <your-token>

{
  "app_ids": [1, 2, 3],
  "format": "react",
  "options": {
    "project_structure": "multi-file",
    "include_tests": false
  }
}
```

#### Get Available Export Formats
```http
GET /api/export-formats/
Authorization: Bearer <your-token>
```

**Response:**
```json
{
  "formats": [
    {
      "value": "react",
      "label": "React",
      "description": "Modern React with hooks",
      "icon": "⚛️",
      "supports_typescript": true,
      "supports_tests": true,
      "supports_storybook": true,
      "style_frameworks": ["styled-components", "emotion", "tailwind", "css-modules"]
    }
  ],
  "style_frameworks": [...],
  "state_management": [...],
  "bundlers": [...],
  "package_managers": [...]
}
```

### Backend Integration

```python
from core.enhanced_code_generator import EnhancedCodeGenerator, ExportOptions, ExportFormat, StyleFramework
from core.code_validator import CodeValidator, ValidationLevel
from my_app.services.export_template_service import ExportTemplateService

# Generate code with full options
generator = EnhancedCodeGenerator()
options = ExportOptions(
    format=ExportFormat.REACT_TS,
    typescript=True,
    include_accessibility=True,
    include_tests=True,
    include_storybook=True,
    style_framework=StyleFramework.STYLED_COMPONENTS,
    state_management="redux",
    project_structure="full-project",
    bundler="vite",
    package_manager="pnpm",
    include_docker=True,
    include_ci_cd=True
)

# Generate code
generated_code = generator.generate_code(app_data, options)

# Validate generated code
validator = CodeValidator()
validation_results = validator.validate_code(
    code=generated_code if isinstance(generated_code, str) else generated_code.get('src/App.tsx', ''),
    language='typescript',
    framework='react'
)

# Check validation results
for result in validation_results:
    if result.level == ValidationLevel.ERROR:
        print(f"Error: {result.message}")
    elif result.level == ValidationLevel.WARNING:
        print(f"Warning: {result.message}")

# Export with templates
export_service = ExportTemplateService()
template_export = export_service.export_app_with_templates(
    app_id=1,
    export_format='react-ts',
    options={
        'typescript': True,
        'project_structure': 'full-project',
        'include_tests': True
    },
    user=request.user
)
```

## Configuration Options

### Export Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `format` | string | 'react' | Target export format (see supported formats) |
| `typescript` | boolean | false | Enable TypeScript support with type definitions |
| `include_accessibility` | boolean | true | Include ARIA labels, roles, and semantic HTML |
| `include_tests` | boolean | false | Generate test files (Jest, React Testing Library, etc.) |
| `include_storybook` | boolean | false | Generate Storybook stories for components |
| `style_framework` | string | 'styled-components' | CSS framework to use (see options below) |
| `state_management` | string | 'useState' | State management solution (see options below) |
| `project_structure` | string | 'single-file' | Project organization (single-file, multi-file, full-project) |
| `bundler` | string | 'vite' | Build tool configuration (vite, webpack, parcel) |
| `package_manager` | string | 'npm' | Package manager (npm, yarn, pnpm) |
| `include_docker` | boolean | false | Include Docker configuration and docker-compose |
| `include_ci_cd` | boolean | false | Include CI/CD pipeline templates |

### Supported Export Formats

| Format | Value | TypeScript | Tests | Storybook | Mobile | Backend |
|--------|-------|------------|-------|-----------|--------|---------|
| React | `react` | ✅ | ✅ | ✅ | ❌ | ❌ |
| React + TypeScript | `react-ts` | ✅ | ✅ | ✅ | ❌ | ❌ |
| Vue.js | `vue` | ✅ | ✅ | ✅ | ❌ | ❌ |
| Vue + TypeScript | `vue-ts` | ✅ | ✅ | ✅ | ❌ | ❌ |
| Angular | `angular` | ✅ | ✅ | ❌ | ❌ | ❌ |
| Svelte | `svelte` | ✅ | ✅ | ❌ | ❌ | ❌ |
| Next.js | `next` | ✅ | ✅ | ✅ | ❌ | ❌ |
| Nuxt.js | `nuxt` | ✅ | ✅ | ❌ | ❌ | ❌ |
| HTML/CSS/JS | `html` | ❌ | ❌ | ❌ | ❌ | ❌ |
| React Native | `react-native` | ✅ | ✅ | ✅ | ✅ | ❌ |
| Flutter | `flutter` | ❌ | ✅ | ❌ | ✅ | ❌ |
| Ionic | `ionic` | ✅ | ✅ | ❌ | ✅ | ❌ |
| Express.js API | `express-api` | ✅ | ✅ | ❌ | ❌ | ✅ |
| Django API | `django-api` | ❌ | ✅ | ❌ | ❌ | ✅ |
| FastAPI | `fastapi` | ❌ | ✅ | ❌ | ❌ | ✅ |

### Style Framework Options

| Framework | Value | React | Vue | Angular | HTML | React Native |
|-----------|-------|-------|-----|---------|------|--------------|
| Styled Components | `styled-components` | ✅ | ❌ | ❌ | ❌ | ✅ |
| Emotion | `emotion` | ✅ | ❌ | ❌ | ❌ | ❌ |
| Tailwind CSS | `tailwind` | ✅ | ✅ | ✅ | ✅ | ❌ |
| CSS Modules | `css-modules` | ✅ | ✅ | ✅ | ❌ | ❌ |
| Material-UI | `material-ui` | ✅ | ❌ | ❌ | ❌ | ❌ |
| Chakra UI | `chakra-ui` | ✅ | ❌ | ❌ | ❌ | ❌ |
| Bootstrap | `bootstrap` | ✅ | ✅ | ✅ | ✅ | ❌ |

### State Management Options

| Solution | Value | React | Vue | Angular | Description |
|----------|-------|-------|-----|---------|-------------|
| React Hooks | `useState` | ✅ | ❌ | ❌ | Built-in React state management |
| Redux Toolkit | `redux` | ✅ | ❌ | ❌ | Predictable state container |
| Zustand | `zustand` | ✅ | ❌ | ❌ | Lightweight state management |
| React Context | `context` | ✅ | ❌ | ❌ | Built-in context API |
| Vuex | `vuex` | ❌ | ✅ | ❌ | Vue state management |
| Pinia | `pinia` | ❌ | ✅ | ❌ | Modern Vue state management |
| NgRx | `ngrx` | ❌ | ❌ | ✅ | Angular state management |

## Framework-Specific Code Examples

### React with TypeScript
```typescript
// Generated App.tsx
import React, { useState } from 'react';
import styled from 'styled-components';

interface ButtonProps {
  text: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}

const StyledButton = styled.button<{ variant: string }>`
  background-color: ${props => props.variant === 'primary' ? '#007bff' : '#6c757d'};
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
`;

const Button: React.FC<ButtonProps> = ({ text, onClick, variant = 'primary' }) => (
  <StyledButton variant={variant} onClick={onClick} aria-label={text}>
    {text}
  </StyledButton>
);

const App: React.FC = () => {
  const [count, setCount] = useState(0);

  return (
    <div className="app" role="main">
      <h1>My App</h1>
      <p>Count: {count}</p>
      <Button
        text="Increment"
        onClick={() => setCount(count + 1)}
      />
    </div>
  );
};

export default App;
```

### Vue 3 with TypeScript
```vue
<!-- Generated App.vue -->
<template>
  <div class="app" role="main">
    <h1>{{ title }}</h1>
    <p>Count: {{ count }}</p>
    <button
      @click="increment"
      :class="['btn', `btn-${variant}`]"
      :aria-label="`Increment count, current value is ${count}`"
    >
      Increment
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface Props {
  title?: string;
  variant?: 'primary' | 'secondary';
}

const props = withDefaults(defineProps<Props>(), {
  title: 'My App',
  variant: 'primary'
});

const count = ref(0);

const increment = () => {
  count.value++;
};
</script>

<style scoped>
.app {
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
}

.btn:hover {
  opacity: 0.8;
}

.btn-secondary {
  background-color: #6c757d;
}
</style>
```

### React Native
```typescript
// Generated App.tsx
import React, { useState } from 'react';
import {
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary';
}

const Button: React.FC<ButtonProps> = ({ title, onPress, variant = 'primary' }) => (
  <TouchableOpacity
    style={[styles.button, variant === 'secondary' && styles.buttonSecondary]}
    onPress={onPress}
    accessibilityLabel={title}
    accessibilityRole="button"
  >
    <Text style={styles.buttonText}>{title}</Text>
  </TouchableOpacity>
);

const App: React.FC = () => {
  const [count, setCount] = useState(0);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />
      <ScrollView contentInsetAdjustmentBehavior="automatic">
        <View style={styles.content}>
          <Text style={styles.title}>My App</Text>
          <Text style={styles.count}>Count: {count}</Text>
          <Button
            title="Increment"
            onPress={() => setCount(count + 1)}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    padding: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  count: {
    fontSize: 18,
    marginBottom: 16,
  },
  button: {
    backgroundColor: '#007bff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  buttonSecondary: {
    backgroundColor: '#6c757d',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default App;
```

## Quality Assurance

### Code Validation

The export system includes comprehensive code validation:

- **Syntax Validation** - Ensures generated code has valid syntax for target language/framework
- **Framework Compliance** - Validates framework-specific patterns and best practices
- **Accessibility Checks** - Ensures WCAG 2.1 AA compliance with proper ARIA attributes
- **Security Validation** - Checks for common security issues (XSS, injection vulnerabilities)
- **Performance Optimization** - Validates performance best practices (bundle size, lazy loading)
- **Type Safety** - TypeScript type checking and interface validation
- **Code Style** - ESLint and Prettier compliance for consistent formatting

### Testing

Comprehensive test suite includes:

- **Unit Tests** - Test individual components and functions with Jest/Vitest
- **Integration Tests** - Test complete export pipeline with real app data
- **Validation Tests** - Test code quality assurance and validation rules
- **Performance Tests** - Test export performance with large applications (1000+ components)
- **Cross-Framework Tests** - Ensure consistency across different export formats
- **Accessibility Tests** - Automated accessibility testing with axe-core

## Integration with Drag-and-Drop Interface

### Component Mapping

The enhanced export system seamlessly integrates with App Builder's drag-and-drop interface:

```javascript
// Component mapping from drag-and-drop to export
const componentMapping = {
  'Button': {
    react: 'button',
    vue: 'button',
    angular: 'button',
    'react-native': 'TouchableOpacity',
    flutter: 'ElevatedButton'
  },
  'Text': {
    react: 'p',
    vue: 'p',
    angular: 'p',
    'react-native': 'Text',
    flutter: 'Text'
  },
  'Input': {
    react: 'input',
    vue: 'input',
    angular: 'input',
    'react-native': 'TextInput',
    flutter: 'TextField'
  }
};
```

### Template System Integration

Export functionality works with both Layout Templates and App Templates:

```javascript
// Export app with templates
const exportWithTemplates = async (appId, format, options) => {
  const response = await fetch('/api/export-template/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      app_id: appId,
      format: format,
      options: {
        ...options,
        include_templates: true,
        template_optimization: true
      }
    })
  });

  return response.json();
};
```

### Real-time Preview Integration

Export preview updates in real-time as users modify components:

```javascript
// Real-time export preview
const useExportPreview = (appData, format, options) => {
  const [preview, setPreview] = useState('');

  useEffect(() => {
    const generatePreview = async () => {
      try {
        const result = await exportService.clientSideExport(appData, format, options);
        setPreview(result.code);
      } catch (error) {
        console.error('Preview generation failed:', error);
      }
    };

    generatePreview();
  }, [appData, format, options]);

  return preview;
};
```

## Best Practices

### Code Generation

1. **Use TypeScript** for better type safety, IntelliSense, and developer experience
2. **Enable Accessibility** to ensure WCAG 2.1 AA compliance and inclusive applications
3. **Include Tests** for better code quality, maintainability, and confidence in deployments
4. **Use Modern Frameworks** for better performance, security, and developer ecosystem
5. **Follow Naming Conventions** for consistent code organization and team collaboration
6. **Implement Error Boundaries** for robust error handling in production applications
7. **Use Semantic HTML** for better SEO and screen reader compatibility

### Project Structure

1. **Single File** - For simple prototypes, demos, and proof-of-concepts
2. **Multi-File** - For organized development projects with component separation
3. **Full Project** - For production-ready applications with complete tooling setup

### Performance Optimization

1. **Optimize Bundle Size** - Use tree shaking, code splitting, and dynamic imports
2. **Enable Caching** - Configure proper caching strategies for static assets
3. **Use Modern Build Tools** - Vite for development speed, Webpack 5 for production optimization
4. **Minimize Dependencies** - Only include necessary packages and use bundle analyzers
5. **Implement Lazy Loading** - Load components and routes on demand
6. **Optimize Images** - Use modern formats (WebP, AVIF) and responsive images
7. **Enable Compression** - Use Gzip/Brotli compression for smaller file sizes

### Security Best Practices

1. **Sanitize User Input** - Prevent XSS attacks with proper input validation
2. **Use HTTPS** - Ensure secure communication in production
3. **Implement CSP** - Content Security Policy headers to prevent injection attacks
4. **Regular Updates** - Keep dependencies updated to patch security vulnerabilities
5. **Environment Variables** - Store sensitive configuration in environment variables
6. **Authentication** - Implement proper authentication and authorization

## Troubleshooting

### Common Issues

#### Export Fails
**Symptoms:** Export request returns error or times out
- **Check app data structure** - Ensure components, layouts, and styles are properly formatted
- **Verify export format compatibility** - Confirm the format supports your selected options
- **Review validation errors** - Check the response for specific validation failures
- **Check authentication** - Ensure valid Bearer token is provided
- **Verify app permissions** - Confirm user has access to the app being exported

#### Generated Code Issues
**Symptoms:** Code doesn't compile or run correctly
- **Run code validation** - Use the built-in validator to check for syntax errors
- **Check framework-specific requirements** - Ensure all required dependencies are included
- **Verify accessibility compliance** - Check ARIA attributes and semantic HTML
- **Review TypeScript types** - Ensure type definitions are correct and complete
- **Check import statements** - Verify all imports are valid and available

#### Performance Issues
**Symptoms:** Export takes too long or fails with timeout
- **Reduce component complexity** - Simplify complex nested structures
- **Use pagination for large apps** - Export in smaller chunks for apps with 100+ components
- **Optimize export options** - Disable unnecessary features like tests or Storybook
- **Use batch export** - For multiple apps, use the batch endpoint
- **Check server resources** - Ensure adequate memory and CPU on the server

#### Template Integration Issues
**Symptoms:** Templates not applied correctly in export
- **Verify template compatibility** - Ensure template supports the target framework
- **Check template data structure** - Confirm template JSON is valid
- **Review template permissions** - Ensure user has access to the template
- **Validate template version** - Use the latest template version

### Error Messages

| Error Code | Error Message | Cause | Solution |
|------------|---------------|-------|----------|
| `INVALID_FORMAT` | "Invalid export format" | Unsupported format specified | Use supported format from `/api/export-formats/` |
| `TEMPLATE_NOT_FOUND` | "Template not found" | Missing or inaccessible template | Check template ID and permissions |
| `VALIDATION_FAILED` | "Code validation failed" | Generated code has quality issues | Review validation results and fix issues |
| `EXPORT_TIMEOUT` | "Export operation timed out" | Large application or server overload | Reduce complexity or use batch export |
| `INSUFFICIENT_PERMISSIONS` | "Access denied" | User lacks permissions | Check app ownership or sharing settings |
| `INVALID_APP_DATA` | "Invalid app data structure" | Malformed app data | Validate app data structure |
| `DEPENDENCY_ERROR` | "Missing dependencies" | Required packages not available | Check package.json and install dependencies |
| `COMPILATION_ERROR` | "Code compilation failed" | Syntax or type errors in generated code | Review generated code and fix syntax issues |

### Debug Mode

Enable debug mode for detailed error information:

```javascript
// Frontend debug mode
const exportOptions = {
  ...options,
  debug: true,
  verbose_logging: true
};

// Backend debug logging
import logging
logging.getLogger('core.enhanced_code_generator').setLevel(logging.DEBUG)
```

### Performance Monitoring

Monitor export performance:

```javascript
// Track export performance
const startTime = performance.now();
const result = await exportService.enhancedExport(appData, format, options);
const endTime = performance.now();
console.log(`Export took ${endTime - startTime} milliseconds`);

// Server-side monitoring
from django.utils import timezone
start_time = timezone.now()
# ... export logic ...
end_time = timezone.now()
logger.info(f"Export completed in {(end_time - start_time).total_seconds()}s")
```

## API Reference

### EnhancedCodeGenerator

```python
class EnhancedCodeGenerator:
    """Enhanced code generator with support for multiple frameworks and modern practices"""

    def __init__(self):
        """Initialize the code generator with template cache"""
        self.template_cache = {}

    def generate_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str], bytes]:
        """
        Generate code based on app data and export options

        Args:
            app_data: Application data structure containing components, layouts, styles, and data
            options: Export configuration options

        Returns:
            Generated code as string (single-file), dict of files (multi-file), or zip bytes (full-project)

        Raises:
            ValueError: If app_data structure is invalid
            NotImplementedError: If export format is not supported
        """

    def _generate_react_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
        """Generate React code with modern best practices"""

    def _generate_vue_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
        """Generate Vue.js code with Composition API"""

    def _generate_angular_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Dict[str, str]:
        """Generate Angular code with TypeScript"""

    def _generate_react_native_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
        """Generate React Native code for mobile development"""

    def _generate_flutter_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
        """Generate Flutter code in Dart"""
```

### CodeValidator

```python
class CodeValidator:
    """Comprehensive code validator for multiple programming languages and frameworks"""

    def __init__(self):
        """Initialize validator with framework-specific rules"""
        self.validation_rules = {
            'react': self._get_react_rules(),
            'vue': self._get_vue_rules(),
            'angular': self._get_angular_rules(),
            # ... other frameworks
        }

    def validate_code(self, code: str, language: str, framework: str = None) -> List[ValidationResult]:
        """
        Validate code for syntax, style, and best practices

        Args:
            code: The code to validate
            language: Programming language (js, ts, py, dart, html, css)
            framework: Framework (react, vue, angular, etc.)

        Returns:
            List of validation results with level, message, and rule information
        """

    def validate_project_structure(self, files: Dict[str, str], framework: str) -> List[ValidationResult]:
        """
        Validate complete project structure

        Args:
            files: Dictionary of file paths to file contents
            framework: Target framework for validation

        Returns:
            List of validation results for the entire project
        """

    def validate_accessibility(self, code: str, framework: str) -> List[ValidationResult]:
        """Validate accessibility compliance (WCAG 2.1 AA)"""

    def validate_security(self, code: str, language: str) -> List[ValidationResult]:
        """Validate security best practices and common vulnerabilities"""

    def validate_performance(self, code: str, framework: str) -> List[ValidationResult]:
        """Validate performance best practices"""
```

### ExportTemplateService

```python
class ExportTemplateService:
    """Service for exporting apps and templates with enhanced functionality"""

    def __init__(self):
        """Initialize service with code generator and validator"""
        self.code_generator = EnhancedCodeGenerator()
        self.validator = CodeValidator()

    def export_app_with_templates(self, app_id: int, export_format: str, options: Dict, user=None) -> Dict:
        """
        Export app with template integration

        Args:
            app_id: ID of the app to export
            export_format: Target export format
            options: Export configuration options
            user: User performing the export (for permissions)

        Returns:
            Dictionary containing generated code, metadata, and export information
        """

    def export_template_as_project(self, template_id: int, template_type: str, export_format: str, options: Dict, user=None) -> Dict:
        """
        Export template as standalone project

        Args:
            template_id: ID of the template to export
            template_type: Type of template (layout, app, component)
            export_format: Target export format
            options: Export configuration options
            user: User performing the export

        Returns:
            Dictionary containing project files and metadata
        """

    def batch_export_apps(self, app_ids: List[int], export_format: str, options: Dict, user=None) -> bytes:
        """
        Export multiple apps as a single zip file

        Args:
            app_ids: List of app IDs to export
            export_format: Target export format for all apps
            options: Export configuration options
            user: User performing the export

        Returns:
            Zip file bytes containing all exported apps
        """
```

### ExportOptions

```python
@dataclass
class ExportOptions:
    """Configuration options for code export"""
    format: ExportFormat
    typescript: bool = False
    include_accessibility: bool = True
    include_tests: bool = False
    include_storybook: bool = False
    style_framework: StyleFramework = StyleFramework.STYLED_COMPONENTS
    state_management: str = "useState"
    project_structure: str = "single-file"
    bundler: str = "vite"
    package_manager: str = "npm"
    include_docker: bool = False
    include_ci_cd: bool = False
    target_directory: Optional[str] = None
```

### ValidationResult

```python
@dataclass
class ValidationResult:
    """Result of code validation"""
    level: ValidationLevel  # ERROR, WARNING, INFO
    message: str
    rule: str
    line_number: Optional[int] = None
    column_number: Optional[int] = None
    suggestion: Optional[str] = None
```

## Multi-File Project Structure Examples

### React TypeScript Full Project
```
my-app/
├── public/
│   ├── index.html
│   └── favicon.ico
├── src/
│   ├── components/
│   │   ├── Button/
│   │   │   ├── Button.tsx
│   │   │   ├── Button.test.tsx
│   │   │   ├── Button.stories.tsx
│   │   │   └── index.ts
│   │   └── index.ts
│   ├── hooks/
│   │   └── useAppState.ts
│   ├── types/
│   │   └── index.ts
│   ├── utils/
│   │   └── helpers.ts
│   ├── App.tsx
│   ├── App.test.tsx
│   ├── App.css
│   └── index.tsx
├── .eslintrc.js
├── .prettierrc
├── tsconfig.json
├── package.json
├── vite.config.ts
├── Dockerfile
├── docker-compose.yml
├── .github/
│   └── workflows/
│       └── ci.yml
└── README.md
```

### Vue 3 TypeScript Full Project
```
my-vue-app/
├── public/
│   └── index.html
├── src/
│   ├── components/
│   │   ├── Button.vue
│   │   └── __tests__/
│   │       └── Button.spec.ts
│   ├── composables/
│   │   └── useCounter.ts
│   ├── types/
│   │   └── index.ts
│   ├── App.vue
│   └── main.ts
├── .eslintrc.js
├── tsconfig.json
├── vite.config.ts
├── package.json
└── README.md
```

### React Native TypeScript Project
```
MyReactNativeApp/
├── src/
│   ├── components/
│   │   ├── Button/
│   │   │   ├── Button.tsx
│   │   │   ├── Button.test.tsx
│   │   │   └── index.ts
│   │   └── index.ts
│   ├── screens/
│   │   └── HomeScreen.tsx
│   ├── navigation/
│   │   └── AppNavigator.tsx
│   ├── types/
│   │   └── index.ts
│   └── App.tsx
├── __tests__/
│   └── App.test.tsx
├── android/
├── ios/
├── metro.config.js
├── package.json
├── tsconfig.json
└── README.md
```

## Contributing

### Adding New Export Formats

1. **Implement Generator Method** - Add format-specific generation logic in `EnhancedCodeGenerator`
   ```python
   def _generate_new_framework_code(self, app_data: Dict[str, Any], options: ExportOptions) -> Union[str, Dict[str, str]]:
       # Implementation here
   ```

2. **Add Format to Enum** - Update `ExportFormat` enum with new format
   ```python
   class ExportFormat(Enum):
       NEW_FRAMEWORK = "new-framework"
   ```

3. **Add Validation Rules** - Define validation rules for the format in `CodeValidator`
   ```python
   def _get_new_framework_rules(self) -> List[ValidationRule]:
       # Framework-specific validation rules
   ```

4. **Create Tests** - Add comprehensive tests for the new format
   ```python
   def test_new_framework_export(self):
       # Test implementation
   ```

5. **Update Frontend** - Add format to frontend export options
   ```javascript
   const newFormat = {
     value: 'new-framework',
     label: 'New Framework',
     description: 'Description of the framework',
     icon: '🆕'
   };
   ```

6. **Update Documentation** - Document the new format and its options

### Improving Code Quality

1. **Add Validation Rules** - Enhance code quality checks for better generated code
2. **Optimize Performance** - Improve generation speed and memory usage
3. **Enhance Accessibility** - Add more accessibility features and WCAG compliance
4. **Update Dependencies** - Keep frameworks and tools current with security patches
5. **Improve Error Handling** - Add better error messages and recovery mechanisms
6. **Add Metrics** - Implement performance monitoring and usage analytics

### Development Setup

1. **Clone Repository** - Get the latest code from the repository
2. **Install Dependencies** - Run `pip install -r requirements.txt` and `npm install`
3. **Run Tests** - Execute `python manage.py test` and `npm test`
4. **Start Development Server** - Run `python manage.py runserver` and `npm start`
5. **Make Changes** - Implement your improvements
6. **Test Changes** - Ensure all tests pass and add new tests
7. **Submit Pull Request** - Create PR with detailed description

### Code Style Guidelines

- **Python**: Follow PEP 8 with Black formatting
- **JavaScript/TypeScript**: Use ESLint and Prettier
- **Documentation**: Use clear, concise language with examples
- **Tests**: Write comprehensive tests with good coverage
- **Commit Messages**: Use conventional commit format

## License

This enhanced export functionality is part of the App Builder project and follows the same licensing terms. The project is open source and welcomes contributions from the community.

## Support

For support and questions:

- **Documentation**: Check this guide and other documentation files
- **Issues**: Report bugs and feature requests on GitHub
- **Discussions**: Join community discussions for help and ideas
- **Email**: Contact the development team for enterprise support

## Changelog

### Version 2.0.0 (Current)
- Added TypeScript support for all frameworks
- Implemented React Native and Flutter export
- Added comprehensive validation system
- Enhanced accessibility features
- Added Docker and CI/CD support
- Improved template integration

### Version 1.0.0
- Initial release with React, Vue, and Angular support
- Basic export functionality
- Simple validation system
